/**
 * Trongrid API Integration for USDT TRC20 Transaction Verification
 *
 * This module provides utilities to interact with the Trongrid API
 * to verify USDT TRC20 transactions on the Tron blockchain.
 * Supports both Mainnet and Testnet configurations.
 */

import { adminSettingsDb } from './database';
import { fromHex } from 'tron-format-address';

const TRONGRID_API_KEY = process.env.TRONGRID_API_KEY; // Optional, for higher rate limits

// Network configuration interface
interface TronNetworkConfig {
  apiUrl: string;
  usdtContract: string;
  network: 'mainnet' | 'testnet';
}

// Rate limiting configuration
const RATE_LIMIT_DELAY = 1000; // 1 second between requests
let lastRequestTime = 0;

/**
 * Get current Tron network configuration from admin settings
 */
async function getTronNetworkConfig(): Promise<TronNetworkConfig> {
  try {
    const network = await adminSettingsDb.get('tronNetwork') || 'testnet';
    const mainnetApiUrl = await adminSettingsDb.get('tronMainnetApiUrl') || 'https://api.trongrid.io';
    const testnetApiUrl = await adminSettingsDb.get('tronTestnetApiUrl') || 'https://api.shasta.trongrid.io';
    const mainnetContract = await adminSettingsDb.get('usdtMainnetContract') || 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
    const testnetContract = await adminSettingsDb.get('usdtTestnetContract') || 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs';

    const isMainnet = network === 'mainnet';

    return {
      apiUrl: isMainnet ? mainnetApiUrl : testnetApiUrl,
      usdtContract: isMainnet ? mainnetContract : testnetContract,
      network: network as 'mainnet' | 'testnet'
    };
  } catch (error) {
    console.error('Error getting Tron network config, using testnet defaults:', error);
    // Fallback to testnet configuration
    return {
      apiUrl: 'https://api.shasta.trongrid.io',
      usdtContract: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs',
      network: 'testnet'
    };
  }
}

interface TronTransaction {
  txID: string;
  blockNumber: number;
  blockTimeStamp: number;
  contractResult: string[];
  receipt: {
    result: string;
  };
  log: Array<{
    address: string;
    topics: string[];
    data: string;
  }>;
}

interface TronTransactionInfo {
  id: string;
  fee: number;
  blockNumber: number;
  blockTimeStamp: number;
  contractResult: string[];
  receipt: {
    result: string;
  };
  log: Array<{
    address: string;
    topics: string[];
    data: string;
  }>;
}

interface TronAccountInfo {
  address: string;
  balance: number;
  create_time: number;
  latest_opration_time: number;
}

interface USDTTransferDetails {
  isValid: boolean;
  amount: number;
  fromAddress: string;
  toAddress: string;
  contractAddress: string;
  blockNumber: number;
  blockTimestamp: number;
  confirmations: number;
  transactionId: string;
}

/**
 * Rate limiting helper to prevent API abuse
 */
async function rateLimitDelay(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  lastRequestTime = Date.now();
}

/**
 * Make HTTP request to Trongrid API with proper headers and error handling
 */
async function makeApiRequest(endpoint: string, networkConfig?: TronNetworkConfig): Promise<any> {
  await rateLimitDelay();

  // Get network config if not provided
  if (!networkConfig) {
    networkConfig = await getTronNetworkConfig();
  }

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (TRONGRID_API_KEY) {
    headers['TRON-PRO-API-KEY'] = TRONGRID_API_KEY;
  }

  const response = await fetch(`${networkConfig.apiUrl}${endpoint}`, {
    method: 'GET',
    headers,
  });

  if (!response.ok) {
    throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Get transaction details by transaction ID
 */
export async function getTransactionById(txId: string, networkConfig?: TronNetworkConfig): Promise<TronTransaction | null> {
  try {
    const config = networkConfig || await getTronNetworkConfig();
    const response = await fetch(`${config.apiUrl}/walletsolidity/gettransactionbyid`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        value: txId
      })
    });

    if (!response.ok) {
      throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.txID ? data : null;
  } catch (error) {
    console.error('Error fetching transaction:', error);
    return null;
  }
}

/**
 * Get transaction info (including receipt) by transaction ID
 */
export async function getTransactionInfo(txId: string, networkConfig?: TronNetworkConfig): Promise<TronTransactionInfo | null> {
  try {
    const config = networkConfig || await getTronNetworkConfig();
    const response = await fetch(`${config.apiUrl}/walletsolidity/gettransactioninfobyid`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        value: txId
      })
    });

    if (!response.ok) {
      throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.id ? data : null;
  } catch (error) {
    console.error('Error fetching transaction info:', error);
    return null;
  }
}

/**
 * Get current block number
 */
export async function getCurrentBlock(networkConfig?: TronNetworkConfig): Promise<{ blockNumber: number } | null> {
  try {
    const config = networkConfig || await getTronNetworkConfig();
    const response = await fetch(`${config.apiUrl}/walletsolidity/getnowblock`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.block_header ? { blockNumber: data.block_header.raw_data.number } : null;
  } catch (error) {
    console.error('Error fetching current block:', error);
    return null;
  }
}

/**
 * Convert hex string to decimal number
 */
function hexToDecimal(hex: string): number {
  return parseInt(hex, 16);
}

/**
 * Convert Tron address from hex to base58
 */
function hexToTronAddress(hex: string): string {
  try {
    // Remove '0x' prefix if present
    let cleanHex = hex.startsWith('0x') ? hex.slice(2) : hex;

    // Ensure we have a 40-character hex string (20 bytes)
    if (cleanHex.length < 40) {
      cleanHex = '0'.repeat(40 - cleanHex.length) + cleanHex;
    }

    // Add the Tron address prefix (0x41 for mainnet/testnet)
    const addressHex = '41' + cleanHex;

    // Convert to base58
    return fromHex(addressHex);
  } catch (error) {
    console.error('Error converting hex to Tron address:', error);
    // Fallback to a recognizable format if conversion fails
    return `T${hex.slice(-30)}`;
  }
}

/**
 * Parse USDT TRC20 transfer from transaction logs
 */
function parseUSDTTransfer(
  logs: Array<{ address: string; topics: string[]; data: string }>,
  usdtContract: string
): {
  amount: number;
  fromAddress: string;
  toAddress: string;
} | null {
  console.log('Parsing USDT transfer from logs:', logs.length, 'Contract:', usdtContract);

  // Find the USDT transfer log
  const usdtLog = logs.find(log => {
    // Convert log address from hex to base58 for comparison
    const logAddressBase58 = hexToTronAddress(log.address);
    const isUSDTContract = logAddressBase58.toLowerCase() === usdtContract.toLowerCase();
    const hasCorrectTopics = log.topics.length >= 3;
    const isTransferEvent = log.topics[0] === 'ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';

    console.log('Log check:', {
      address: log.address,
      addressBase58: logAddressBase58,
      usdtContract,
      isUSDTContract,
      hasCorrectTopics,
      isTransferEvent,
      topicsLength: log.topics.length,
      firstTopic: log.topics[0]
    });

    return isUSDTContract && hasCorrectTopics && isTransferEvent;
  });

  if (!usdtLog) {
    console.log('No USDT transfer log found');
    return null;
  }

  try {
    console.log('Found USDT log:', usdtLog);

    // Parse transfer details from log
    // Topics[1] = from address (padded), Topics[2] = to address (padded)
    const fromAddressHex = usdtLog.topics[1].slice(26); // Remove 0x and padding
    const toAddressHex = usdtLog.topics[2].slice(26); // Remove 0x and padding
    const amountHex = usdtLog.data.startsWith('0x') ? usdtLog.data.slice(2) : usdtLog.data;

    const fromAddress = hexToTronAddress(fromAddressHex);
    const toAddress = hexToTronAddress(toAddressHex);
    const amount = hexToDecimal(amountHex) / 1000000; // USDT has 6 decimals

    console.log('Parsed transfer:', {
      fromAddress,
      toAddress,
      amount,
      fromAddressHex,
      toAddressHex,
      amountHex
    });

    return {
      amount,
      fromAddress,
      toAddress,
    };
  } catch (error) {
    console.error('Error parsing USDT transfer:', error);
    return null;
  }
}

/**
 * Verify USDT TRC20 transaction and extract transfer details
 */
export async function verifyUSDTTransaction(
  txId: string,
  expectedToAddress: string,
  minConfirmations: number = 1
): Promise<USDTTransferDetails> {
  // Get current network configuration
  const networkConfig = await getTronNetworkConfig();

  console.log('Verifying USDT transaction:', {
    txId,
    expectedToAddress,
    minConfirmations,
    network: networkConfig.network,
    apiUrl: networkConfig.apiUrl,
    usdtContract: networkConfig.usdtContract
  });

  try {
    // Get transaction details
    const transaction = await getTransactionById(txId, networkConfig);
    console.log('Transaction details:', transaction);

    if (!transaction) {
      console.log('Transaction not found');
      return {
        isValid: false,
        amount: 0,
        fromAddress: '',
        toAddress: '',
        contractAddress: networkConfig.usdtContract,
        blockNumber: 0,
        blockTimestamp: 0,
        confirmations: 0,
        transactionId: txId,
      };
    }

    // Get transaction info for receipt and confirmations
    const transactionInfo = await getTransactionInfo(txId, networkConfig);
    console.log('Transaction info:', transactionInfo);

    if (!transactionInfo) {
      console.log('Transaction info not found');
      return {
        isValid: false,
        amount: 0,
        fromAddress: '',
        toAddress: '',
        contractAddress: networkConfig.usdtContract,
        blockNumber: 0,
        blockTimestamp: 0,
        confirmations: 0,
        transactionId: txId,
      };
    }

    // Check if transaction was successful
    console.log('Transaction receipt result:', transactionInfo.receipt?.result);
    if (transactionInfo.receipt?.result !== 'SUCCESS') {
      console.log('Transaction failed or not successful');
      return {
        isValid: false,
        amount: 0,
        fromAddress: '',
        toAddress: '',
        contractAddress: networkConfig.usdtContract,
        blockNumber: transactionInfo.blockNumber,
        blockTimestamp: transactionInfo.blockTimeStamp,
        confirmations: 0,
        transactionId: txId,
      };
    }

    // Parse USDT transfer from logs
    const transferDetails = parseUSDTTransfer(transactionInfo.log || [], networkConfig.usdtContract);
    if (!transferDetails) {
      console.log('No USDT transfer details found');
      return {
        isValid: false,
        amount: 0,
        fromAddress: '',
        toAddress: '',
        contractAddress: networkConfig.usdtContract,
        blockNumber: transactionInfo.blockNumber,
        blockTimestamp: transactionInfo.blockTimeStamp,
        confirmations: 0,
        transactionId: txId,
      };
    }

    // Calculate confirmations using block numbers
    const currentBlock = await getCurrentBlock(networkConfig);
    let confirmations = 0;

    if (currentBlock && transactionInfo.blockNumber) {
      confirmations = currentBlock.blockNumber - transactionInfo.blockNumber;
    }

    console.log('Confirmation calculation:', {
      currentBlockNumber: currentBlock?.blockNumber,
      transactionBlockNumber: transactionInfo.blockNumber,
      confirmations,
      minConfirmations
    });

    // Verify the recipient address matches expected address
    const isValidRecipient = transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase();

    console.log('Address verification:', {
      transferToAddress: transferDetails.toAddress,
      expectedToAddress,
      isValidRecipient,
      addressesMatch: transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase()
    });

    const isValid = isValidRecipient && confirmations >= minConfirmations && transferDetails.amount > 0;

    console.log('Final verification result:', {
      isValid,
      isValidRecipient,
      confirmations,
      minConfirmations,
      amount: transferDetails.amount
    });

    return {
      isValid,
      amount: transferDetails.amount,
      fromAddress: transferDetails.fromAddress,
      toAddress: transferDetails.toAddress,
      contractAddress: networkConfig.usdtContract,
      blockNumber: transactionInfo.blockNumber,
      blockTimestamp: transactionInfo.blockTimeStamp,
      confirmations: Math.max(0, confirmations),
      transactionId: txId,
    };

  } catch (error) {
    console.error('Error verifying USDT transaction:', error);
    return {
      isValid: false,
      amount: 0,
      fromAddress: '',
      toAddress: '',
      contractAddress: networkConfig.usdtContract,
      blockNumber: 0,
      blockTimestamp: 0,
      confirmations: 0,
      transactionId: txId,
    };
  }
}

/**
 * Validate Tron transaction ID format
 */
export function isValidTronTransactionId(txId: string): boolean {
  // Tron transaction IDs are 64-character hexadecimal strings
  const tronTxRegex = /^[a-fA-F0-9]{64}$/;
  return tronTxRegex.test(txId);
}

/**
 * Validate Tron address format
 */
export function isValidTronAddress(address: string): boolean {
  // Tron addresses start with 'T' and are 34 characters long
  const tronAddressRegex = /^T[A-Za-z1-9]{33}$/;
  return tronAddressRegex.test(address);
}

/**
 * Get account information by address
 */
export async function getAccountInfo(address: string, networkConfig?: TronNetworkConfig): Promise<TronAccountInfo | null> {
  try {
    const data = await makeApiRequest(`/v1/accounts/${address}`, networkConfig);
    return data.data && data.data.length > 0 ? data.data[0] : null;
  } catch (error) {
    console.error('Error fetching account info:', error);
    return null;
  }
}

/**
 * Get current network configuration (exported for external use)
 */
export async function getCurrentNetworkConfig(): Promise<TronNetworkConfig> {
  return await getTronNetworkConfig();
}
