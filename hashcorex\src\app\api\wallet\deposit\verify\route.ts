import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { 
  depositTransactionDb, 
  walletBalanceDb, 
  transactionDb, 
  adminSettingsDb, 
  systemLogDb 
} from '@/lib/database';
import { verifyUSDTTransaction, isValidTronTransactionId } from '@/lib/trongrid';

// Rate limiting map to prevent abuse
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5; // 5 requests per minute per user

function checkRateLimit(userId: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(userId);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(userId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  userLimit.count++;
  return true;
}

// POST - Verify deposit transaction
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check rate limiting
    if (!checkRateLimit(user.id)) {
      return NextResponse.json(
        { success: false, error: 'Too many verification requests. Please wait before trying again.' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { transactionId } = body;

    // Validation
    if (!transactionId || typeof transactionId !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Transaction ID is required' },
        { status: 400 }
      );
    }

    if (!isValidTronTransactionId(transactionId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid Tron transaction ID format' },
        { status: 400 }
      );
    }

    // Check if transaction already exists
    const existingDeposit = await depositTransactionDb.findByTransactionId(transactionId);
    if (existingDeposit) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Transaction already processed',
          data: {
            status: existingDeposit.status,
            amount: existingDeposit.usdtAmount,
            createdAt: existingDeposit.createdAt,
          }
        },
        { status: 400 }
      );
    }

    // Get deposit settings - try both camelCase and UPPER_CASE keys for compatibility
    let depositAddress = await adminSettingsDb.get('usdtDepositAddress');
    if (!depositAddress) {
      depositAddress = await adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');
    }

    // Clean deposit address - remove quotes and extra characters
    if (depositAddress) {
      depositAddress = depositAddress.replace(/['"]/g, '').trim();
    }

    let minDepositAmount = await adminSettingsDb.get('minDepositAmount');
    if (!minDepositAmount) {
      minDepositAmount = await adminSettingsDb.get('MIN_DEPOSIT_AMOUNT');
    }
    minDepositAmount = parseFloat(minDepositAmount || '10');

    let maxDepositAmount = await adminSettingsDb.get('maxDepositAmount');
    if (!maxDepositAmount) {
      maxDepositAmount = await adminSettingsDb.get('MAX_DEPOSIT_AMOUNT');
    }
    maxDepositAmount = parseFloat(maxDepositAmount || '10000');

    let depositEnabled = await adminSettingsDb.get('depositEnabled');
    if (!depositEnabled) {
      depositEnabled = await adminSettingsDb.get('DEPOSIT_ENABLED');
    }
    depositEnabled = depositEnabled === 'true' || depositEnabled === true;

    let minConfirmations = await adminSettingsDb.get('minConfirmations');
    if (!minConfirmations) {
      minConfirmations = await adminSettingsDb.get('MIN_CONFIRMATIONS');
    }
    minConfirmations = parseInt(minConfirmations || '1');

    if (!depositEnabled) {
      return NextResponse.json(
        { success: false, error: 'Deposits are currently disabled' },
        { status: 503 }
      );
    }

    if (!depositAddress) {
      return NextResponse.json(
        { success: false, error: 'Deposit address not configured. Please contact support.' },
        { status: 503 }
      );
    }

    // Log verification attempt
    await systemLogDb.create({
      action: 'DEPOSIT_VERIFICATION_ATTEMPT',
      userId: user.id,
      details: { transactionId },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    // Verify transaction with Trongrid API (use 1 confirmation to get basic transaction info)
    const verificationResult = await verifyUSDTTransaction(
      transactionId,
      depositAddress,
      1 // Get basic transaction info first
    );

    // Check if transaction exists and is valid (regardless of confirmations)
    if (!verificationResult.isValid && verificationResult.confirmations === 0) {
      // Transaction not found or completely invalid
      await depositTransactionDb.create({
        userId: user.id,
        transactionId,
        amount: 0,
        usdtAmount: verificationResult.amount,
        tronAddress: depositAddress,
        senderAddress: verificationResult.fromAddress,
        blockNumber: verificationResult.blockNumber.toString(),
        blockTimestamp: new Date(verificationResult.blockTimestamp),
        confirmations: verificationResult.confirmations,
      });

      await depositTransactionDb.markAsFailed(
        transactionId,
        'Transaction not found or invalid'
      );

      return NextResponse.json(
        {
          success: false,
          error: 'Transaction not found or invalid',
          details: {
            isValid: verificationResult.isValid,
            amount: verificationResult.amount,
            confirmations: verificationResult.confirmations,
            minConfirmations,
            toAddress: verificationResult.toAddress,
            expectedAddress: depositAddress,
          }
        },
        { status: 400 }
      );
    }

    // Check if transaction has valid recipient address and amount
    const hasValidRecipient = verificationResult.toAddress.toLowerCase().includes(depositAddress.toLowerCase().slice(1, 10)) ||
                              depositAddress.toLowerCase().includes(verificationResult.toAddress.toLowerCase().slice(1, 10));

    if (!hasValidRecipient || verificationResult.amount <= 0) {
      await depositTransactionDb.create({
        userId: user.id,
        transactionId,
        amount: verificationResult.amount,
        usdtAmount: verificationResult.amount,
        tronAddress: depositAddress,
        senderAddress: verificationResult.fromAddress,
        blockNumber: verificationResult.blockNumber.toString(),
        blockTimestamp: new Date(verificationResult.blockTimestamp),
        confirmations: verificationResult.confirmations,
      });

      await depositTransactionDb.markAsFailed(
        transactionId,
        'Invalid recipient address or amount'
      );

      return NextResponse.json(
        {
          success: false,
          error: 'Invalid recipient address or amount',
          details: {
            isValid: false,
            amount: verificationResult.amount,
            confirmations: verificationResult.confirmations,
            minConfirmations,
            toAddress: verificationResult.toAddress,
            expectedAddress: depositAddress,
          }
        },
        { status: 400 }
      );
    }

    // Validate deposit amount
    if (verificationResult.amount < minDepositAmount) {
      await depositTransactionDb.create({
        userId: user.id,
        transactionId,
        amount: verificationResult.amount,
        usdtAmount: verificationResult.amount,
        tronAddress: depositAddress,
        senderAddress: verificationResult.fromAddress,
        blockNumber: verificationResult.blockNumber.toString(),
        blockTimestamp: new Date(verificationResult.blockTimestamp),
        confirmations: verificationResult.confirmations,
      });

      await depositTransactionDb.markAsFailed(
        transactionId,
        `Deposit amount ${verificationResult.amount} USDT is below minimum ${minDepositAmount} USDT`
      );

      return NextResponse.json(
        { 
          success: false, 
          error: `Deposit amount must be at least ${minDepositAmount} USDT`,
          data: {
            amount: verificationResult.amount,
            minAmount: minDepositAmount,
          }
        },
        { status: 400 }
      );
    }

    if (verificationResult.amount > maxDepositAmount) {
      await depositTransactionDb.create({
        userId: user.id,
        transactionId,
        amount: verificationResult.amount,
        usdtAmount: verificationResult.amount,
        tronAddress: depositAddress,
        senderAddress: verificationResult.fromAddress,
        blockNumber: verificationResult.blockNumber.toString(),
        blockTimestamp: new Date(verificationResult.blockTimestamp),
        confirmations: verificationResult.confirmations,
      });

      await depositTransactionDb.markAsFailed(
        transactionId,
        `Deposit amount ${verificationResult.amount} USDT exceeds maximum ${maxDepositAmount} USDT`
      );

      return NextResponse.json(
        { 
          success: false, 
          error: `Deposit amount cannot exceed ${maxDepositAmount} USDT`,
          data: {
            amount: verificationResult.amount,
            maxAmount: maxDepositAmount,
          }
        },
        { status: 400 }
      );
    }

    // Create deposit record
    const depositRecord = await depositTransactionDb.create({
      userId: user.id,
      transactionId,
      amount: verificationResult.amount,
      usdtAmount: verificationResult.amount,
      tronAddress: depositAddress,
      senderAddress: verificationResult.fromAddress,
      blockNumber: verificationResult.blockNumber.toString(),
      blockTimestamp: new Date(verificationResult.blockTimestamp),
      confirmations: verificationResult.confirmations,
    });

    // Check if transaction has enough confirmations
    if (verificationResult.confirmations >= minConfirmations) {
      // Sufficient confirmations - complete the deposit immediately
      await depositTransactionDb.updateStatus(transactionId, 'CONFIRMED', {
        verifiedAt: new Date(),
        processedAt: new Date(),
      });

      // Credit user's wallet balance
      await walletBalanceDb.addDeposit(user.id, verificationResult.amount);

      // Create transaction record for balance tracking
      await transactionDb.create({
        userId: user.id,
        type: 'DEPOSIT',
        amount: verificationResult.amount,
        description: `USDT TRC20 Deposit - TX: ${transactionId}`,
        status: 'COMPLETED',
      });

      // Log successful deposit
      await systemLogDb.create({
        action: 'DEPOSIT_COMPLETED',
        userId: user.id,
        details: {
          transactionId,
          amount: verificationResult.amount,
          senderAddress: verificationResult.fromAddress,
          blockNumber: verificationResult.blockNumber,
          confirmations: verificationResult.confirmations,
          minConfirmations,
          processedBy: 'USER_VERIFICATION',
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      });

      return NextResponse.json({
        success: true,
        message: 'Deposit verified and credited successfully',
        data: {
          transactionId,
          amount: verificationResult.amount,
          status: 'COMPLETED',
          confirmations: verificationResult.confirmations,
          minConfirmations,
          blockNumber: verificationResult.blockNumber,
          processedAt: new Date(),
        },
      });
    } else {
      // Insufficient confirmations - keep as pending
      await systemLogDb.create({
        action: 'DEPOSIT_PENDING',
        userId: user.id,
        details: {
          transactionId,
          amount: verificationResult.amount,
          senderAddress: verificationResult.fromAddress,
          blockNumber: verificationResult.blockNumber,
          confirmations: verificationResult.confirmations,
          minConfirmations,
          status: 'PENDING',
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      });

      return NextResponse.json({
        success: true,
        message: `Deposit transaction verified but pending confirmations. Current: ${verificationResult.confirmations}/${minConfirmations} confirmations. Your wallet will be credited automatically once the required confirmations are reached.`,
        data: {
          transactionId,
          amount: verificationResult.amount,
          status: 'PENDING',
          confirmations: verificationResult.confirmations,
          minConfirmations,
          blockNumber: verificationResult.blockNumber,
          estimatedCompletionTime: 'Within 30 minutes',
        },
      });
    }

  } catch (error) {
    console.error('Deposit verification error:', error);
    
    // Log error for debugging
    if (request.headers.get('authorization')) {
      try {
        const { user } = await authenticateRequest(request);
        if (user) {
          await systemLogDb.create({
            action: 'DEPOSIT_VERIFICATION_ERROR',
            userId: user.id,
            details: { error: error instanceof Error ? error.message : 'Unknown error' },
            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
          });
        }
      } catch (logError) {
        console.error('Failed to log error:', logError);
      }
    }

    return NextResponse.json(
      { success: false, error: 'Failed to verify deposit. Please try again later.' },
      { status: 500 }
    );
  }
}
