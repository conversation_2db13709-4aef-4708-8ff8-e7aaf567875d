'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';
import { Grid, GridItem } from '@/components/layout';
import { TrendingUp, DollarSign, Clock, Calendar } from 'lucide-react';
import { formatCurrency, formatDateTime, getTimeUntilNextPayout } from '@/lib/utils';

interface EarningsData {
  totalEarnings: number;
  pendingEarnings: number;
  miningEarnings: number;
  referralEarnings: number;
  estimatedEarnings: {
    next7Days: number;
    next30Days: number;
    next365Days: number;
  };
  recentEarnings: Array<{
    id: string;
    type: string;
    amount: number;
    description: string;
    createdAt: string;
  }>;
  earningsBreakdown: {
    mining: number;
    directReferral: number;
    binaryBonus: number;
  };
}

export const EarningsTracker: React.FC = () => {
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeUntilPayout, setTimeUntilPayout] = useState(getTimeUntilNextPayout());

  useEffect(() => {
    fetchEarningsData();
    
    // Update countdown every second
    const interval = setInterval(() => {
      setTimeUntilPayout(getTimeUntilNextPayout());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchEarningsData = async () => {
    try {
      const response = await fetch('/api/earnings', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setEarningsData(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch earnings data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-32 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (!earningsData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load earnings data</p>
        </CardContent>
      </Card>
    );
  }

  const getEarningsTypeColor = (type: string) => {
    switch (type) {
      case 'MINING_EARNINGS':
        return 'text-solar-600';
      case 'DIRECT_REFERRAL':
        return 'text-eco-600';
      case 'BINARY_BONUS':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const getEarningsTypeLabel = (type: string) => {
    switch (type) {
      case 'MINING_EARNINGS':
        return 'Mining';
      case 'DIRECT_REFERRAL':
        return 'Direct Referral';
      case 'BINARY_BONUS':
        return 'Binary Bonus';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-8">
      {/* Earnings Overview */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Earnings Overview</h2>
        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Earnings</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {formatCurrency(earningsData.totalEarnings)}
                  </p>
                </div>
                <div className="h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center">
                  <DollarSign className="h-7 w-7 text-eco-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Pending Earnings</p>
                  <p className="text-3xl font-bold text-solar-600">
                    {formatCurrency(earningsData.pendingEarnings)}
                  </p>
                </div>
                <div className="h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center">
                  <Clock className="h-7 w-7 text-solar-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Mining Earnings</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {formatCurrency(earningsData.miningEarnings)}
                  </p>
                </div>
                <div className="h-14 w-14 bg-gray-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-7 w-7 text-gray-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Referral Earnings</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {formatCurrency(earningsData.referralEarnings)}
                  </p>
                </div>
                <div className="h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-7 w-7 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      <Grid cols={{ default: 1, lg: 2 }} gap={6}>
        {/* Next Payout Countdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-solar-500" />
              <span>Next Payout</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Weekly payout every Saturday at 15:00 UTC
              </p>
              <div className="grid grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilPayout.days}
                  </div>
                  <div className="text-xs text-gray-500">Days</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilPayout.hours}
                  </div>
                  <div className="text-xs text-gray-500">Hours</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilPayout.minutes}
                  </div>
                  <div className="text-xs text-gray-500">Minutes</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilPayout.seconds}
                  </div>
                  <div className="text-xs text-gray-500">Seconds</div>
                </div>
              </div>
              {earningsData.pendingEarnings > 0 && (
                <div className="mt-4 p-3 bg-solar-50 rounded-lg">
                  <p className="text-sm text-solar-700">
                    <strong>{formatCurrency(earningsData.pendingEarnings)}</strong> will be transferred to your wallet
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Estimated Earnings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-eco-500" />
              <span>Estimated Earnings</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Next 7 Days</span>
                <span className="font-semibold text-eco-600">
                  {formatCurrency(earningsData.estimatedEarnings.next7Days)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Next 30 Days</span>
                <span className="font-semibold text-eco-600">
                  {formatCurrency(earningsData.estimatedEarnings.next30Days)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Next 365 Days</span>
                <span className="font-semibold text-eco-600">
                  {formatCurrency(earningsData.estimatedEarnings.next365Days)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Next 2 Years</span>
                <span className="font-semibold text-eco-600">
                  {formatCurrency(earningsData.estimatedEarnings.next2Years)}
                </span>
              </div>
            </div>
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600">
                * Estimates based on current mining units and average ROI
              </p>
            </div>
          </CardContent>
        </Card>
      </Grid>

      {/* Recent Earnings */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Earnings</CardTitle>
        </CardHeader>
        <CardContent>
          {earningsData.recentEarnings.length > 0 ? (
            <div className="space-y-3">
              {earningsData.recentEarnings.slice(0, 10).map((earning) => (
                <div key={earning.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-medium ${getEarningsTypeColor(earning.type)}`}>
                        {getEarningsTypeLabel(earning.type)}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatDateTime(earning.createdAt)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{earning.description}</p>
                  </div>
                  <div className="text-right">
                    <span className="font-semibold text-eco-600">
                      +{formatCurrency(earning.amount)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No earnings yet</p>
              <p className="text-sm text-gray-400 mt-1">
                Purchase mining units to start earning
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
