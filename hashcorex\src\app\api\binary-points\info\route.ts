import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { binaryPointsDb, adminSettingsDb } from '@/lib/database';
import { prisma } from '@/lib/prisma';

// GET - Fetch comprehensive binary points information
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get user's binary points
    const userBinaryPoints = await binaryPointsDb.findByUserId(user.id);
    
    // Get system settings
    const maxPointsPerSide = parseFloat(await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '2000');
    const pointValue = parseFloat(await adminSettingsDb.get('BINARY_POINT_VALUE') || '10'); // Dynamic point value from settings

    // Calculate user's current status
    const leftPoints = userBinaryPoints?.leftPoints || 0;
    const rightPoints = userBinaryPoints?.rightPoints || 0;
    const matchedPoints = userBinaryPoints?.matchedPoints || 0;
    const totalMatched = userBinaryPoints?.totalMatched || 0;

    // Apply caps and calculate matchable points
    const leftPointsCapped = Math.min(leftPoints, maxPointsPerSide);
    const rightPointsCapped = Math.min(rightPoints, maxPointsPerSide);
    const currentMatchablePoints = Math.min(leftPointsCapped, rightPointsCapped);

    // Calculate pressure-out amounts
    const leftPressureOut = Math.max(0, leftPoints - maxPointsPerSide);
    const rightPressureOut = Math.max(0, rightPoints - maxPointsPerSide);
    const totalPressureOut = leftPressureOut + rightPressureOut;

    // Calculate estimated earnings with fixed point value
    const estimatedEarnings = currentMatchablePoints * pointValue;

    // Get historical data (last 4 weeks)
    const fourWeeksAgo = new Date(Date.now() - 28 * 24 * 60 * 60 * 1000);
    const historicalMatches = await prisma.transaction.findMany({
      where: {
        userId: user.id,
        type: 'BINARY_BONUS',
        status: 'COMPLETED',
        createdAt: { gte: fourWeeksAgo },
      },
      orderBy: { createdAt: 'desc' },
      take: 4,
    });

    // Calculate progress percentages
    const leftProgress = (leftPointsCapped / maxPointsPerSide) * 100;
    const rightProgress = (rightPointsCapped / maxPointsPerSide) * 100;

    // Determine warnings
    const warnings = [];
    if (leftPoints > maxPointsPerSide * 0.9) {
      warnings.push(`Left side approaching cap (${leftPoints.toFixed(0)}/${maxPointsPerSide})`);
    }
    if (rightPoints > maxPointsPerSide * 0.9) {
      warnings.push(`Right side approaching cap (${rightPoints.toFixed(0)}/${maxPointsPerSide})`);
    }
    if (totalPressureOut > 0) {
      warnings.push(`${totalPressureOut.toFixed(0)} points will be flushed due to pressure-out`);
    }

    const binaryPointsInfo = {
      // Current status
      currentPoints: {
        left: leftPoints,
        right: rightPoints,
        leftCapped: leftPointsCapped,
        rightCapped: rightPointsCapped,
        matchable: currentMatchablePoints,
        matched: matchedPoints,
        totalMatched: totalMatched,
      },

      // System limits
      limits: {
        maxPointsPerSide,
        pointValue,
      },

      // Progress indicators
      progress: {
        leftProgress,
        rightProgress,
        leftNearCap: leftProgress > 90,
        rightNearCap: rightProgress > 90,
      },

      // Pressure-out information
      pressureOut: {
        leftAmount: leftPressureOut,
        rightAmount: rightPressureOut,
        totalAmount: totalPressureOut,
        willOccur: totalPressureOut > 0,
      },

      // Earnings estimation
      earnings: {
        estimatedPayout: estimatedEarnings,
        pointValue,
        matchablePoints: currentMatchablePoints,
      },

      // Historical data
      history: {
        recentMatches: historicalMatches.map(match => ({
          amount: match.amount,
          date: match.createdAt,
          description: match.description,
        })),
        averageWeeklyEarnings: historicalMatches.length > 0 
          ? historicalMatches.reduce((sum, match) => sum + match.amount, 0) / historicalMatches.length 
          : 0,
      },

      // Warnings and alerts
      warnings,
      hasWarnings: warnings.length > 0,

      // Next matching info
      nextMatching: {
        schedule: 'Weekly on Saturdays at 15:00 UTC',
        description: 'Binary points are matched weekly, not daily',
      },
    };

    return NextResponse.json({
      success: true,
      data: binaryPointsInfo,
    });

  } catch (error: any) {
    console.error('Binary points info fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch binary points information' },
      { status: 500 }
    );
  }
}
