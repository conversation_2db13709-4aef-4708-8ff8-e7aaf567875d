"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.toHex = exports.fromHex = void 0;
const crypto_1 = __importDefault(require("crypto"));
const base58_1 = require("./base58");
const sha256 = (msg) => crypto_1.default.createHash('sha256').update(Buffer.from(msg, 'hex')).digest('hex');
const fromHex = (hex) => {
    const addr = `41${hex.substring(2)}`;
    const doubleSha256 = sha256(sha256(addr));
    const checkSum = doubleSha256.substring(0, 8);
    const address = Buffer.from(addr + checkSum, 'hex');
    return (0, base58_1.encode58)(address);
};
exports.fromHex = fromHex;
const toHex = (base58Sting) => {
    if (base58Sting.length <= 4)
        throw new Error('Invalid address provided');
    let address = Buffer.from((0, base58_1.decode58)(base58Sting)).toString('hex');
    const checkSum = address.substring(address.length - 8, address.length);
    address = address.substring(0, address.length - 8);
    const checkSum1 = sha256(sha256(address)).substring(0, 8);
    if (`${checkSum}` === `${checkSum1}`)
        return `0x${address.substring(2)}`;
    throw new Error('Invalid address provided');
};
exports.toHex = toHex;
