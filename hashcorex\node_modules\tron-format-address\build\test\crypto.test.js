"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const chai_1 = require("chai");
const crypto_1 = require("../lib/crypto");
describe('From Base58 to Hexadecimal', () => {
    it('#1', () => {
        const expectedHex = '******************************************';
        const hex = (0, crypto_1.toHex)('TCNtTa1rveKkovHR2ebABu4K66U6ocUCZX');
        (0, chai_1.expect)(hex).to.equal(expectedHex);
    });
    it('#2', () => {
        const expectedHex = '******************************************';
        const hex = (0, crypto_1.toHex)('TEEFn7rQqx4Xc3GL1Bx27A155xAj7w5W7a');
        (0, chai_1.expect)(hex).to.equal(expectedHex);
    });
    it('#3', () => {
        const expectedHex = '******************************************';
        const hex = (0, crypto_1.toHex)('TGgd7pXdZALo9GyT4pmF2tT6JRf7ETWVcL');
        (0, chai_1.expect)(hex).to.equal(expectedHex);
    });
});
describe('From Hexadecimal to Base58', () => {
    it('#1', () => {
        const expectedBase58 = 'TCNtTa1rveKkovHR2ebABu4K66U6ocUCZX';
        const base58 = (0, crypto_1.fromHex)('******************************************');
        (0, chai_1.expect)(base58).to.equal(expectedBase58);
    });
    it('#2', () => {
        const expectedBase58 = 'TEEFn7rQqx4Xc3GL1Bx27A155xAj7w5W7a';
        const base58 = (0, crypto_1.fromHex)('******************************************');
        (0, chai_1.expect)(base58).to.equal(expectedBase58);
    });
    it('#3', () => {
        const expectedBase58 = 'TGgd7pXdZALo9GyT4pmF2tT6JRf7ETWVcL';
        const base58 = (0, crypto_1.fromHex)('******************************************');
        (0, chai_1.expect)(base58).to.equal(expectedBase58);
    });
});
