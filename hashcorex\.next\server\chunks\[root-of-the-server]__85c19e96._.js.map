{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/database.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { User, MiningUnit, Transaction, Referral, BinaryPoints, WalletBalance, DepositTransaction, DepositStatus } from '@/types';\n\n// User Database Operations\nexport const userDb = {\n  async create(data: {\n    email: string;\n    firstName: string;\n    lastName: string;\n    password: string;\n    referralId?: string;\n  }) {\n    return await prisma.user.create({\n      data: {\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: data.password,\n        referralId: data.referralId || undefined,\n      },\n    });\n  },\n\n  async findByEmail(email: string) {\n    return await prisma.user.findUnique({\n      where: { email },\n      include: {\n        miningUnits: true,\n        transactions: true,\n        binaryPoints: true,\n      },\n    });\n  },\n\n  async findById(id: string) {\n    return await prisma.user.findUnique({\n      where: { id },\n      include: {\n        miningUnits: true,\n        transactions: true,\n        binaryPoints: true,\n      },\n    });\n  },\n\n  async findByReferralId(referralId: string) {\n    return await prisma.user.findUnique({\n      where: { referralId },\n    });\n  },\n\n  async update(id: string, data: Partial<{\n    firstName: string;\n    lastName: string;\n    email: string;\n    role: 'USER' | 'ADMIN';\n    isActive: boolean;\n    kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';\n  }>) {\n    return await prisma.user.update({\n      where: { id },\n      data,\n    });\n  },\n\n  async updateKYCStatus(userId: string, status: 'PENDING' | 'APPROVED' | 'REJECTED') {\n    return await prisma.user.update({\n      where: { id: userId },\n      data: { kycStatus: status },\n    });\n  },\n};\n\n// Mining Unit Database Operations\nexport const miningUnitDb = {\n  async create(data: {\n    userId: string;\n    thsAmount: number;\n    investmentAmount: number;\n    dailyROI: number;\n  }) {\n    const expiryDate = new Date();\n    expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n\n    return await prisma.miningUnit.create({\n      data: {\n        userId: data.userId,\n        thsAmount: data.thsAmount,\n        investmentAmount: data.investmentAmount,\n        dailyROI: data.dailyROI,\n        expiryDate,\n      },\n    });\n  },\n\n  async findActiveByUserId(userId: string) {\n    return await prisma.miningUnit.findMany({\n      where: {\n        userId,\n        status: 'ACTIVE',\n        expiryDate: {\n          gt: new Date(),\n        },\n      },\n    });\n  },\n\n  async updateTotalEarned(unitId: string, amount: number) {\n    return await prisma.miningUnit.update({\n      where: { id: unitId },\n      data: {\n        totalEarned: {\n          increment: amount,\n        },\n      },\n    });\n  },\n\n  async expireUnit(unitId: string) {\n    return await prisma.miningUnit.update({\n      where: { id: unitId },\n      data: { status: 'EXPIRED' },\n    });\n  },\n};\n\n// Transaction Database Operations\nexport const transactionDb = {\n  async create(data: {\n    userId: string;\n    type: 'MINING_EARNINGS' | 'DIRECT_REFERRAL' | 'BINARY_BONUS' | 'DEPOSIT' | 'WITHDRAWAL' | 'PURCHASE' | 'ADMIN_CREDIT' | 'ADMIN_DEBIT';\n    amount: number;\n    description: string;\n    reference?: string;\n    status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';\n  }) {\n    return await prisma.transaction.create({\n      data: {\n        userId: data.userId,\n        type: data.type,\n        amount: data.amount,\n        description: data.description,\n        reference: data.reference,\n        status: data.status || 'PENDING',\n      },\n    });\n  },\n\n  async findByUserId(userId: string, filters?: {\n    types?: string[];\n    status?: string;\n    limit?: number;\n    offset?: number;\n    includeUser?: boolean;\n  }) {\n    const where: any = { userId };\n\n    if (filters?.types && filters.types.length > 0) {\n      where.type = { in: filters.types };\n    }\n\n    if (filters?.status) {\n      where.status = filters.status;\n    }\n\n    const include = filters?.includeUser ? {\n      user: {\n        select: {\n          id: true,\n          email: true,\n          firstName: true,\n          lastName: true,\n        },\n      },\n    } : undefined;\n\n    return await prisma.transaction.findMany({\n      where,\n      include,\n      orderBy: { createdAt: 'desc' },\n      take: filters?.limit || 50,\n      skip: filters?.offset,\n    });\n  },\n\n  async updateStatus(transactionId: string, status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED') {\n    return await prisma.transaction.update({\n      where: { id: transactionId },\n      data: { status },\n    });\n  },\n};\n\n// Referral Database Operations\nexport const referralDb = {\n  async create(data: {\n    referrerId: string;\n    referredId: string;\n    placementSide: 'LEFT' | 'RIGHT';\n  }) {\n    return await prisma.referral.create({\n      data: {\n        referrerId: data.referrerId,\n        referredId: data.referredId,\n        placementSide: data.placementSide,\n      },\n    });\n  },\n\n  async findByReferrerId(referrerId: string) {\n    return await prisma.referral.findMany({\n      where: { referrerId },\n      include: {\n        referred: {\n          select: {\n            id: true,\n            email: true,\n            createdAt: true,\n          },\n        },\n      },\n    });\n  },\n};\n\n// Binary Points Database Operations\nexport const binaryPointsDb = {\n  async upsert(data: {\n    userId: string;\n    leftPoints?: number;\n    rightPoints?: number;\n  }) {\n    // Round to 2 decimal places to ensure precision\n    const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n    const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n\n    return await prisma.binaryPoints.upsert({\n      where: { userId: data.userId },\n      update: {\n        leftPoints: leftPoints !== undefined ? { increment: leftPoints } : undefined,\n        rightPoints: rightPoints !== undefined ? { increment: rightPoints } : undefined,\n      },\n      create: {\n        userId: data.userId,\n        leftPoints: leftPoints || 0,\n        rightPoints: rightPoints || 0,\n      },\n    });\n  },\n\n  async findByUserId(userId: string) {\n    return await prisma.binaryPoints.findUnique({\n      where: { userId },\n    });\n  },\n\n  async resetPoints(userId: string, leftPoints: number, rightPoints: number) {\n    return await prisma.binaryPoints.update({\n      where: { userId },\n      data: {\n        leftPoints,\n        rightPoints,\n        flushDate: new Date(),\n      },\n    });\n  },\n};\n\n// Withdrawal Database Operations\nexport const withdrawalDb = {\n  async create(data: {\n    userId: string;\n    amount: number;\n    usdtAddress: string;\n  }) {\n    return await prisma.withdrawalRequest.create({\n      data: {\n        userId: data.userId,\n        amount: data.amount,\n        usdtAddress: data.usdtAddress,\n      },\n    });\n  },\n\n  async findPending() {\n    return await prisma.withdrawalRequest.findMany({\n      where: { status: 'PENDING' },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            kycStatus: true,\n          },\n        },\n      },\n      orderBy: { createdAt: 'asc' },\n    });\n  },\n\n  async updateStatus(\n    requestId: string, \n    status: 'APPROVED' | 'REJECTED' | 'COMPLETED',\n    processedBy?: string,\n    txid?: string,\n    rejectionReason?: string\n  ) {\n    return await prisma.withdrawalRequest.update({\n      where: { id: requestId },\n      data: {\n        status,\n        processedBy,\n        txid,\n        rejectionReason,\n        processedAt: new Date(),\n      },\n    });\n  },\n};\n\n// Admin Settings Database Operations\nexport const adminSettingsDb = {\n  async get(key: string) {\n    const setting = await prisma.adminSettings.findUnique({\n      where: { key },\n    });\n    return setting?.value;\n  },\n\n  async set(key: string, value: string, updatedBy?: string) {\n    return await prisma.adminSettings.upsert({\n      where: { key },\n      update: { value },\n      create: { key, value },\n    });\n  },\n\n  async getAll() {\n    return await prisma.adminSettings.findMany();\n  },\n};\n\n// System Logs\nexport const systemLogDb = {\n  async create(data: {\n    action: string;\n    userId?: string;\n    adminId?: string;\n    details?: any;\n    ipAddress?: string;\n    userAgent?: string;\n  }) {\n    return await prisma.systemLog.create({\n      data: {\n        action: data.action,\n        userId: data.userId,\n        adminId: data.adminId,\n        details: data.details ? JSON.stringify(data.details) : null,\n        ipAddress: data.ipAddress,\n        userAgent: data.userAgent,\n      },\n    });\n  },\n};\n\n// Wallet Balance Database Operations\nexport const walletBalanceDb = {\n  async getOrCreate(userId: string): Promise<WalletBalance> {\n    let walletBalance = await prisma.walletBalance.findUnique({\n      where: { userId },\n    });\n\n    if (!walletBalance) {\n      walletBalance = await prisma.walletBalance.create({\n        data: {\n          userId,\n          availableBalance: 0,\n          pendingBalance: 0,\n          totalDeposits: 0,\n          totalWithdrawals: 0,\n          totalEarnings: 0,\n        },\n      });\n    }\n\n    return walletBalance as WalletBalance;\n  },\n\n  async updateBalance(userId: string, updates: {\n    availableBalance?: number;\n    pendingBalance?: number;\n    totalDeposits?: number;\n    totalWithdrawals?: number;\n    totalEarnings?: number;\n  }) {\n    return await prisma.walletBalance.update({\n      where: { userId },\n      data: {\n        ...updates,\n        lastUpdated: new Date(),\n      },\n    });\n  },\n\n  async addDeposit(userId: string, amount: number) {\n    const wallet = await this.getOrCreate(userId);\n    return await prisma.walletBalance.update({\n      where: { userId },\n      data: {\n        availableBalance: wallet.availableBalance + amount,\n        totalDeposits: wallet.totalDeposits + amount,\n        lastUpdated: new Date(),\n      },\n    });\n  },\n\n  async addEarnings(userId: string, amount: number) {\n    const wallet = await this.getOrCreate(userId);\n    return await prisma.walletBalance.update({\n      where: { userId },\n      data: {\n        availableBalance: wallet.availableBalance + amount,\n        totalEarnings: wallet.totalEarnings + amount,\n        lastUpdated: new Date(),\n      },\n    });\n  },\n\n  async deductWithdrawal(userId: string, amount: number) {\n    const wallet = await this.getOrCreate(userId);\n    if (wallet.availableBalance < amount) {\n      throw new Error('Insufficient balance');\n    }\n\n    return await prisma.walletBalance.update({\n      where: { userId },\n      data: {\n        availableBalance: wallet.availableBalance - amount,\n        totalWithdrawals: wallet.totalWithdrawals + amount,\n        lastUpdated: new Date(),\n      },\n    });\n  },\n\n  async findByUserId(userId: string) {\n    return await this.getOrCreate(userId);\n  },\n};\n\n// Deposit Transaction Database Operations\nexport const depositTransactionDb = {\n  async create(data: {\n    userId: string;\n    transactionId: string;\n    amount: number;\n    usdtAmount: number;\n    tronAddress: string;\n    senderAddress?: string;\n    blockNumber?: string;\n    blockTimestamp?: Date;\n    confirmations?: number;\n  }) {\n    return await prisma.depositTransaction.create({\n      data: {\n        userId: data.userId,\n        transactionId: data.transactionId,\n        amount: data.amount,\n        usdtAmount: data.usdtAmount,\n        tronAddress: data.tronAddress,\n        senderAddress: data.senderAddress,\n        blockNumber: data.blockNumber,\n        blockTimestamp: data.blockTimestamp,\n        confirmations: data.confirmations || 0,\n        status: 'PENDING',\n      },\n    });\n  },\n\n  async findByTransactionId(transactionId: string) {\n    return await prisma.depositTransaction.findUnique({\n      where: { transactionId },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  async findByUserId(userId: string, filters?: {\n    status?: DepositStatus;\n    limit?: number;\n    offset?: number;\n  }) {\n    const where: any = { userId };\n\n    if (filters?.status) {\n      where.status = filters.status;\n    }\n\n    return await prisma.depositTransaction.findMany({\n      where,\n      orderBy: { createdAt: 'desc' },\n      take: filters?.limit || 50,\n      skip: filters?.offset,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  async findAll(filters?: {\n    status?: DepositStatus;\n    limit?: number;\n    offset?: number;\n  }) {\n    const where: any = {};\n\n    if (filters?.status) {\n      where.status = filters.status;\n    }\n\n    return await prisma.depositTransaction.findMany({\n      where,\n      orderBy: { createdAt: 'desc' },\n      take: filters?.limit || 100,\n      skip: filters?.offset,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  async updateStatus(\n    transactionId: string,\n    status: DepositStatus,\n    updates?: {\n      verifiedAt?: Date;\n      processedAt?: Date;\n      failureReason?: string;\n      confirmations?: number;\n    }\n  ) {\n    const updateData: any = { status };\n\n    if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n    if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n    if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n    if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n\n    return await prisma.depositTransaction.update({\n      where: { transactionId },\n      data: updateData,\n    });\n  },\n\n  async markAsCompleted(transactionId: string) {\n    return await this.updateStatus(transactionId, 'COMPLETED', {\n      processedAt: new Date(),\n    });\n  },\n\n  async markAsFailed(transactionId: string, reason: string) {\n    return await this.updateStatus(transactionId, 'FAILED', {\n      failureReason: reason,\n      processedAt: new Date(),\n    });\n  },\n\n  async getPendingDeposits() {\n    return await this.findAll({ status: 'PENDING' });\n  },\n\n  async findByStatus(status: DepositStatus) {\n    return await prisma.depositTransaction.findMany({\n      where: { status },\n      orderBy: { createdAt: 'desc' },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  async updateConfirmations(transactionId: string, confirmations: number) {\n    return await prisma.depositTransaction.update({\n      where: { transactionId },\n      data: { confirmations },\n    });\n  },\n\n  async getDepositStats() {\n    const stats = await prisma.depositTransaction.aggregate({\n      _count: {\n        id: true,\n      },\n      _sum: {\n        usdtAmount: true,\n      },\n      where: {\n        status: 'COMPLETED',\n      },\n    });\n\n    const pendingCount = await prisma.depositTransaction.count({\n      where: { status: 'PENDING' },\n    });\n\n    return {\n      totalDeposits: stats._count.id || 0,\n      totalAmount: stats._sum.usdtAmount || 0,\n      pendingDeposits: pendingCount,\n    };\n  },\n};\n\n// Support Ticket Database Operations\nexport const supportTicketDb = {\n  create: async (data: any) => {\n    return await prisma.supportTicket.create({\n      data,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n    });\n  },\n\n  findByUserId: async (userId: string) => {\n    return await prisma.supportTicket.findMany({\n      where: { userId },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n      orderBy: { createdAt: 'desc' },\n    });\n  },\n\n  findById: async (id: string) => {\n    return await prisma.supportTicket.findUnique({\n      where: { id },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n    });\n  },\n\n  findAll: async () => {\n    return await prisma.supportTicket.findMany({\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n      orderBy: { createdAt: 'desc' },\n    });\n  },\n\n  updateStatus: async (id: string, status: any) => {\n    return await prisma.supportTicket.update({\n      where: { id },\n      data: { status, updatedAt: new Date() },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n    });\n  },\n};\n\n// Ticket Response Database Operations\nexport const ticketResponseDb = {\n  create: async (data: any) => {\n    return await prisma.ticketResponse.create({\n      data,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  findByTicketId: async (ticketId: string) => {\n    return await prisma.ticketResponse.findMany({\n      where: { ticketId },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n      orderBy: { createdAt: 'asc' },\n    });\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAIO,MAAM,SAAS;IACpB,MAAM,QAAO,IAMZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,MAAM;gBACJ,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU,IAAI;YACjC;QACF;IACF;IAEA,MAAM,aAAY,KAAa;QAC7B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAM;YACf,SAAS;gBACP,aAAa;gBACb,cAAc;gBACd,cAAc;YAChB;QACF;IACF;IAEA,MAAM,UAAS,EAAU;QACvB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,aAAa;gBACb,cAAc;gBACd,cAAc;YAChB;QACF;IACF;IAEA,MAAM,kBAAiB,UAAkB;QACvC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAW;QACtB;IACF;IAEA,MAAM,QAAO,EAAU,EAAE,IAOvB;QACA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,OAAO;gBAAE;YAAG;YACZ;QACF;IACF;IAEA,MAAM,iBAAgB,MAAc,EAAE,MAA2C;QAC/E,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,WAAW;YAAO;QAC5B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM,QAAO,IAKZ;QACC,MAAM,aAAa,IAAI;QACvB,WAAW,WAAW,CAAC,WAAW,WAAW,KAAK,IAAI,qBAAqB;QAE3E,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,SAAS;gBACzB,kBAAkB,KAAK,gBAAgB;gBACvC,UAAU,KAAK,QAAQ;gBACvB;YACF;QACF;IACF;IAEA,MAAM,oBAAmB,MAAc;QACrC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACtC,OAAO;gBACL;gBACA,QAAQ;gBACR,YAAY;oBACV,IAAI,IAAI;gBACV;YACF;QACF;IACF;IAEA,MAAM,mBAAkB,MAAc,EAAE,MAAc;QACpD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBACJ,aAAa;oBACX,WAAW;gBACb;YACF;QACF;IACF;IAEA,MAAM,YAAW,MAAc;QAC7B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,QAAQ;YAAU;QAC5B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,QAAO,IAOZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YACrC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW;gBAC7B,WAAW,KAAK,SAAS;gBACzB,QAAQ,KAAK,MAAM,IAAI;YACzB;QACF;IACF;IAEA,MAAM,cAAa,MAAc,EAAE,OAMlC;QACC,MAAM,QAAa;YAAE;QAAO;QAE5B,IAAI,SAAS,SAAS,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;YAC9C,MAAM,IAAI,GAAG;gBAAE,IAAI,QAAQ,KAAK;YAAC;QACnC;QAEA,IAAI,SAAS,QAAQ;YACnB,MAAM,MAAM,GAAG,QAAQ,MAAM;QAC/B;QAEA,MAAM,UAAU,SAAS,cAAc;YACrC,MAAM;gBACJ,QAAQ;oBACN,IAAI;oBACJ,OAAO;oBACP,WAAW;oBACX,UAAU;gBACZ;YACF;QACF,IAAI;QAEJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACvC;YACA;YACA,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM,SAAS,SAAS;YACxB,MAAM,SAAS;QACjB;IACF;IAEA,MAAM,cAAa,aAAqB,EAAE,MAAwD;QAChG,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YACrC,OAAO;gBAAE,IAAI;YAAc;YAC3B,MAAM;gBAAE;YAAO;QACjB;IACF;AACF;AAGO,MAAM,aAAa;IACxB,MAAM,QAAO,IAIZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,eAAe,KAAK,aAAa;YACnC;QACF;IACF;IAEA,MAAM,kBAAiB,UAAkB;QACvC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpC,OAAO;gBAAE;YAAW;YACpB,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;oBACb;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,IAIZ;QACC,gDAAgD;QAChD,MAAM,aAAa,KAAK,UAAU,KAAK,YAAY,KAAK,KAAK,CAAC,KAAK,UAAU,GAAG,OAAO,MAAM;QAC7F,MAAM,cAAc,KAAK,WAAW,KAAK,YAAY,KAAK,KAAK,CAAC,KAAK,WAAW,GAAG,OAAO,MAAM;QAEhG,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE,QAAQ,KAAK,MAAM;YAAC;YAC7B,QAAQ;gBACN,YAAY,eAAe,YAAY;oBAAE,WAAW;gBAAW,IAAI;gBACnE,aAAa,gBAAgB,YAAY;oBAAE,WAAW;gBAAY,IAAI;YACxE;YACA,QAAQ;gBACN,QAAQ,KAAK,MAAM;gBACnB,YAAY,cAAc;gBAC1B,aAAa,eAAe;YAC9B;QACF;IACF;IAEA,MAAM,cAAa,MAAc;QAC/B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE;YAAO;QAClB;IACF;IAEA,MAAM,aAAY,MAAc,EAAE,UAAkB,EAAE,WAAmB;QACvE,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ;gBACA;gBACA,WAAW,IAAI;YACjB;QACF;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM,QAAO,IAIZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC3C,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW;YAC/B;QACF;IACF;IAEA,MAAM;QACJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBAAE,QAAQ;YAAU;YAC3B,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;oBACb;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAM;QAC9B;IACF;IAEA,MAAM,cACJ,SAAiB,EACjB,MAA6C,EAC7C,WAAoB,EACpB,IAAa,EACb,eAAwB;QAExB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC3C,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA,aAAa,IAAI;YACnB;QACF;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,KAAI,GAAW;QACnB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACpD,OAAO;gBAAE;YAAI;QACf;QACA,OAAO,SAAS;IAClB;IAEA,MAAM,KAAI,GAAW,EAAE,KAAa,EAAE,SAAkB;QACtD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAI;YACb,QAAQ;gBAAE;YAAM;YAChB,QAAQ;gBAAE;gBAAK;YAAM;QACvB;IACF;IAEA,MAAM;QACJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ;IAC5C;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,IAOZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACnC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;gBACnB,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO,GAAG,KAAK,SAAS,CAAC,KAAK,OAAO,IAAI;gBACvD,WAAW,KAAK,SAAS;gBACzB,WAAW,KAAK,SAAS;YAC3B;QACF;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,aAAY,MAAc;QAC9B,IAAI,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACxD,OAAO;gBAAE;YAAO;QAClB;QAEA,IAAI,CAAC,eAAe;YAClB,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBAChD,MAAM;oBACJ;oBACA,kBAAkB;oBAClB,gBAAgB;oBAChB,eAAe;oBACf,kBAAkB;oBAClB,eAAe;gBACjB;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAc,MAAc,EAAE,OAMnC;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ,GAAG,OAAO;gBACV,aAAa,IAAI;YACnB;QACF;IACF;IAEA,MAAM,YAAW,MAAc,EAAE,MAAc;QAC7C,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ,kBAAkB,OAAO,gBAAgB,GAAG;gBAC5C,eAAe,OAAO,aAAa,GAAG;gBACtC,aAAa,IAAI;YACnB;QACF;IACF;IAEA,MAAM,aAAY,MAAc,EAAE,MAAc;QAC9C,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ,kBAAkB,OAAO,gBAAgB,GAAG;gBAC5C,eAAe,OAAO,aAAa,GAAG;gBACtC,aAAa,IAAI;YACnB;QACF;IACF;IAEA,MAAM,kBAAiB,MAAc,EAAE,MAAc;QACnD,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,IAAI,OAAO,gBAAgB,GAAG,QAAQ;YACpC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ,kBAAkB,OAAO,gBAAgB,GAAG;gBAC5C,kBAAkB,OAAO,gBAAgB,GAAG;gBAC5C,aAAa,IAAI;YACnB;QACF;IACF;IAEA,MAAM,cAAa,MAAc;QAC/B,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC;IAChC;AACF;AAGO,MAAM,uBAAuB;IAClC,MAAM,QAAO,IAUZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,eAAe,KAAK,aAAa;gBACjC,QAAQ,KAAK,MAAM;gBACnB,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;gBAC7B,eAAe,KAAK,aAAa;gBACjC,aAAa,KAAK,WAAW;gBAC7B,gBAAgB,KAAK,cAAc;gBACnC,eAAe,KAAK,aAAa,IAAI;gBACrC,QAAQ;YACV;QACF;IACF;IAEA,MAAM,qBAAoB,aAAqB;QAC7C,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAc;YACvB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,MAAM,cAAa,MAAc,EAAE,OAIlC;QACC,MAAM,QAAa;YAAE;QAAO;QAE5B,IAAI,SAAS,QAAQ;YACnB,MAAM,MAAM,GAAG,QAAQ,MAAM;QAC/B;QAEA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC9C;YACA,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM,SAAS,SAAS;YACxB,MAAM,SAAS;YACf,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,MAAM,SAAQ,OAIb;QACC,MAAM,QAAa,CAAC;QAEpB,IAAI,SAAS,QAAQ;YACnB,MAAM,MAAM,GAAG,QAAQ,MAAM;QAC/B;QAEA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC9C;YACA,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM,SAAS,SAAS;YACxB,MAAM,SAAS;YACf,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,MAAM,cACJ,aAAqB,EACrB,MAAqB,EACrB,OAKC;QAED,MAAM,aAAkB;YAAE;QAAO;QAEjC,IAAI,SAAS,YAAY,WAAW,UAAU,GAAG,QAAQ,UAAU;QACnE,IAAI,SAAS,aAAa,WAAW,WAAW,GAAG,QAAQ,WAAW;QACtE,IAAI,SAAS,eAAe,WAAW,aAAa,GAAG,QAAQ,aAAa;QAC5E,IAAI,SAAS,kBAAkB,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QAE1F,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC5C,OAAO;gBAAE;YAAc;YACvB,MAAM;QACR;IACF;IAEA,MAAM,iBAAgB,aAAqB;QACzC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,aAAa;YACzD,aAAa,IAAI;QACnB;IACF;IAEA,MAAM,cAAa,aAAqB,EAAE,MAAc;QACtD,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,UAAU;YACtD,eAAe;YACf,aAAa,IAAI;QACnB;IACF;IAEA,MAAM;QACJ,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YAAE,QAAQ;QAAU;IAChD;IAEA,MAAM,cAAa,MAAqB;QACtC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC9C,OAAO;gBAAE;YAAO;YAChB,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,MAAM,qBAAoB,aAAqB,EAAE,aAAqB;QACpE,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC5C,OAAO;gBAAE;YAAc;YACvB,MAAM;gBAAE;YAAc;QACxB;IACF;IAEA,MAAM;QACJ,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC;YACtD,QAAQ;gBACN,IAAI;YACN;YACA,MAAM;gBACJ,YAAY;YACd;YACA,OAAO;gBACL,QAAQ;YACV;QACF;QAEA,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACzD,OAAO;gBAAE,QAAQ;YAAU;QAC7B;QAEA,OAAO;YACL,eAAe,MAAM,MAAM,CAAC,EAAE,IAAI;YAClC,aAAa,MAAM,IAAI,CAAC,UAAU,IAAI;YACtC,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,QAAQ,OAAO;QACb,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;IACF;IAEA,cAAc,OAAO;QACnB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACzC,OAAO;gBAAE;YAAO;YAChB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;IACF;IAEA,UAAU,OAAO;QACf,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YAC3C,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;IACF;IAEA,SAAS;QACP,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACzC,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;IACF;IAEA,cAAc,OAAO,IAAY;QAC/B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAG;YACZ,MAAM;gBAAE;gBAAQ,WAAW,IAAI;YAAO;YACtC,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,QAAQ,OAAO;QACb,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACxC;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,gBAAgB,OAAO;QACrB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC1C,OAAO;gBAAE;YAAS;YAClB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAM;QAC9B;IACF;AACF", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { userDb } from './database';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\n// Password utilities\nexport const hashPassword = async (password: string): Promise<string> => {\n  return await bcrypt.hash(password, 12);\n};\n\nexport const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {\n  return await bcrypt.compare(password, hashedPassword);\n};\n\n// JWT utilities\nexport const generateToken = (payload: { userId: string; email: string }): string => {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });\n};\n\nexport const verifyToken = (token: string): { userId: string; email: string } | null => {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; email: string };\n    return decoded;\n  } catch (error) {\n    return null;\n  }\n};\n\n// Generate unique referral ID\nexport const generateReferralId = (): string => {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n  let result = 'HC'; // HashCoreX prefix\n  for (let i = 0; i < 8; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n};\n\n// Authentication middleware\nexport const authenticateRequest = async (request: NextRequest) => {\n  const token = request.headers.get('authorization')?.replace('Bearer ', '') ||\n                request.cookies.get('auth-token')?.value;\n\n  if (!token) {\n    return { authenticated: false, user: null };\n  }\n\n  const decoded = verifyToken(token);\n  if (!decoded) {\n    return { authenticated: false, user: null };\n  }\n\n  const user = await userDb.findByEmail(decoded.email);\n  if (!user) {\n    return { authenticated: false, user: null };\n  }\n\n  return { authenticated: true, user };\n};\n\n// User registration\nexport const registerUser = async (data: {\n  email: string;\n  firstName: string;\n  lastName: string;\n  password: string;\n  referralCode?: string;\n  placementSide?: 'left' | 'right';\n}) => {\n  // Check if user already exists\n  const existingUser = await userDb.findByEmail(data.email);\n  if (existingUser) {\n    throw new Error('User already exists with this email');\n  }\n\n  // Validate referral code if provided\n  let referrerId: string | undefined;\n  if (data.referralCode) {\n    const referrer = await userDb.findByReferralId(data.referralCode);\n    if (!referrer) {\n      throw new Error('Invalid referral code');\n    }\n    referrerId = referrer.id;\n  }\n\n  // Hash password\n  const passwordHash = await hashPassword(data.password);\n\n  // Generate unique referral ID\n  let referralId: string;\n  let isUnique = false;\n  do {\n    referralId = generateReferralId();\n    const existing = await userDb.findByReferralId(referralId);\n    isUnique = !existing;\n  } while (!isUnique);\n\n\n\n  // Create user in PostgreSQL\n  const user = await userDb.create({\n    email: data.email,\n    firstName: data.firstName,\n    lastName: data.lastName,\n    password: passwordHash,\n    referralId,\n  });\n\n  // Create referral relationship if referrer exists\n  if (referrerId) {\n    const { placeUserByReferralType } = await import('./referral');\n\n    // Determine referral type based on placementSide parameter\n    let referralType: 'general' | 'left' | 'right' = 'general';\n    if (data.placementSide === 'left') {\n      referralType = 'left';\n    } else if (data.placementSide === 'right') {\n      referralType = 'right';\n    }\n\n    // Place user using the new unified placement function\n    await placeUserByReferralType(referrerId, user.id, referralType);\n  }\n\n  return {\n    id: user.id,\n    email: user.email,\n    referralId: user.referralId,\n    kycStatus: user.kycStatus,\n  };\n};\n\n// User login\nexport const loginUser = async (data: {\n  email: string;\n  password: string;\n}) => {\n  const user = await userDb.findByEmail(data.email);\n  if (!user) {\n    throw new Error('Invalid email or password');\n  }\n\n\n\n  const isValidPassword = await verifyPassword(data.password, user.password);\n  if (!isValidPassword) {\n    throw new Error('Invalid email or password');\n  }\n\n  const token = generateToken({\n    userId: user.id,\n    email: user.email,\n  });\n\n  return {\n    token,\n    user: {\n      id: user.id,\n      email: user.email,\n      referralId: user.referralId,\n      kycStatus: user.kycStatus,\n    },\n  };\n};\n\n// Password validation\nexport const validatePassword = (password: string): { valid: boolean; errors: string[] } => {\n  const errors: string[] = [];\n\n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n\n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n\n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n\n  return {\n    valid: errors.length === 0,\n    errors,\n  };\n};\n\n// Email validation\nexport const validateEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Session management\nexport const createSession = (userId: string, email: string) => {\n  return generateToken({ userId, email });\n};\n\nexport const validateSession = (token: string) => {\n  return verifyToken(token);\n};\n\n// Admin authentication\nexport const isAdmin = async (userId: string): Promise<boolean> => {\n  const user = await userDb.findById(userId);\n  return user?.role === 'ADMIN';\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAG9C,MAAM,eAAe,OAAO;IACjC,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AACrC;AAEO,MAAM,iBAAiB,OAAO,UAAkB;IACrD,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AACxC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAe;AACnE;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,MAAM,qBAAqB;IAChC,MAAM,QAAQ;IACd,IAAI,SAAS,MAAM,mBAAmB;IACtC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,MAAM,sBAAsB,OAAO;IACxC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW,OACzD,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IAEjD,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,MAAM,UAAU,YAAY;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,KAAK;IACnD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,OAAO;QAAE,eAAe;QAAM;IAAK;AACrC;AAGO,MAAM,eAAe,OAAO;IAQjC,+BAA+B;IAC/B,MAAM,eAAe,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,KAAK;IACxD,IAAI,cAAc;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,qCAAqC;IACrC,IAAI;IACJ,IAAI,KAAK,YAAY,EAAE;QACrB,MAAM,WAAW,MAAM,wHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,KAAK,YAAY;QAChE,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QACA,aAAa,SAAS,EAAE;IAC1B;IAEA,gBAAgB;IAChB,MAAM,eAAe,MAAM,aAAa,KAAK,QAAQ;IAErD,8BAA8B;IAC9B,IAAI;IACJ,IAAI,WAAW;IACf,GAAG;QACD,aAAa;QACb,MAAM,WAAW,MAAM,wHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC;QAC/C,WAAW,CAAC;IACd,QAAS,CAAC,SAAU;IAIpB,4BAA4B;IAC5B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,MAAM,CAAC;QAC/B,OAAO,KAAK,KAAK;QACjB,WAAW,KAAK,SAAS;QACzB,UAAU,KAAK,QAAQ;QACvB,UAAU;QACV;IACF;IAEA,kDAAkD;IAClD,IAAI,YAAY;QACd,MAAM,EAAE,uBAAuB,EAAE,GAAG;QAEpC,2DAA2D;QAC3D,IAAI,eAA6C;QACjD,IAAI,KAAK,aAAa,KAAK,QAAQ;YACjC,eAAe;QACjB,OAAO,IAAI,KAAK,aAAa,KAAK,SAAS;YACzC,eAAe;QACjB;QAEA,sDAAsD;QACtD,MAAM,wBAAwB,YAAY,KAAK,EAAE,EAAE;IACrD;IAEA,OAAO;QACL,IAAI,KAAK,EAAE;QACX,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;IAC3B;AACF;AAGO,MAAM,YAAY,OAAO;IAI9B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,KAAK;IAChD,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAIA,MAAM,kBAAkB,MAAM,eAAe,KAAK,QAAQ,EAAE,KAAK,QAAQ;IACzE,IAAI,CAAC,iBAAiB;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,cAAc;QAC1B,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;IACnB;IAEA,OAAO;QACL;QACA,MAAM;YACJ,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,YAAY,KAAK,UAAU;YAC3B,WAAW,KAAK,SAAS;QAC3B;IACF;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,gBAAgB,CAAC,QAAgB;IAC5C,OAAO,cAAc;QAAE;QAAQ;IAAM;AACvC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,YAAY;AACrB;AAGO,MAAM,UAAU,OAAO;IAC5B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;IACnC,OAAO,MAAM,SAAS;AACxB", "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/referral.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { referralDb, binaryPointsDb, transactionDb, adminSettingsDb, systemLogDb, walletBalanceDb } from './database';\n\n// Check if user has active mining units (for binary tree display)\nexport async function hasActiveMiningUnits(userId: string): Promise<boolean> {\n  try {\n    const activeMiningUnits = await prisma.miningUnit.count({\n      where: {\n        userId,\n        status: 'ACTIVE',\n        expiryDate: {\n          gt: new Date(),\n        },\n      },\n    });\n\n    return activeMiningUnits > 0;\n  } catch (error) {\n    console.error('Error checking active mining units:', error);\n    return false;\n  }\n}\n\n// Calculate total downline count for a specific side\nexport async function calculateDownlineCount(userId: string, side: 'LEFT' | 'RIGHT'): Promise<number> {\n  try {\n    const downlineUsers = await getDownlineUsers(userId, side);\n    return downlineUsers.length;\n  } catch (error) {\n    console.error('Downline count calculation error:', error);\n    return 0;\n  }\n}\n\n// Find the optimal placement position in the weaker leg\nasync function findOptimalPlacementPosition(referrerId: string): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' }> {\n  try {\n    // Calculate total downline counts for both sides\n    const leftDownlineCount = await calculateDownlineCount(referrerId, 'LEFT');\n    const rightDownlineCount = await calculateDownlineCount(referrerId, 'RIGHT');\n\n    // Determine weaker leg based on total downline count\n    const weakerSide: 'LEFT' | 'RIGHT' = leftDownlineCount <= rightDownlineCount ? 'LEFT' : 'RIGHT';\n\n    // Find the next available spot in the weaker leg\n    const availableSpot = await findNextAvailableSpotInLeg(referrerId, weakerSide);\n\n    if (availableSpot) {\n      return availableSpot;\n    }\n\n    // Fallback: if no spot found in weaker leg, try the other side\n    const strongerSide: 'LEFT' | 'RIGHT' = weakerSide === 'LEFT' ? 'RIGHT' : 'LEFT';\n    const fallbackSpot = await findNextAvailableSpotInLeg(referrerId, strongerSide);\n\n    if (fallbackSpot) {\n      return fallbackSpot;\n    }\n\n    // Final fallback: place directly under referrer\n    const existingReferrals = await referralDb.findByReferrerId(referrerId);\n    const hasLeft = existingReferrals.some(r => r.placementSide === 'LEFT');\n    const hasRight = existingReferrals.some(r => r.placementSide === 'RIGHT');\n\n    if (!hasLeft) {\n      return { userId: referrerId, side: 'LEFT' };\n    } else if (!hasRight) {\n      return { userId: referrerId, side: 'RIGHT' };\n    }\n\n    // If both sides are occupied, place in the weaker side\n    return { userId: referrerId, side: weakerSide };\n\n  } catch (error) {\n    console.error('Optimal placement position error:', error);\n    // Fallback to left side\n    return { userId: referrerId, side: 'LEFT' };\n  }\n}\n\n// Enhanced place new user in binary tree with weaker leg algorithm\nexport async function placeUserInBinaryTree(referrerId: string, newUserId: string): Promise<'LEFT' | 'RIGHT'> {\n  try {\n    // Find optimal placement position using advanced weaker leg algorithm\n    const optimalPosition = await findOptimalPlacementPosition(referrerId);\n\n    // Create referral relationship with the optimal parent\n    await referralDb.create({\n      referrerId: optimalPosition.userId,\n      referredId: newUserId,\n      placementSide: optimalPosition.side,\n    });\n\n    // Update the parent's left/right referral IDs\n    const updateData = optimalPosition.side === 'LEFT'\n      ? { leftReferralId: newUserId }\n      : { rightReferralId: newUserId };\n\n    await prisma.user.update({\n      where: { id: optimalPosition.userId },\n      data: updateData,\n    });\n\n    // Create sponsor relationship (separate from binary placement)\n    // The sponsor is always the original referrer, regardless of binary placement\n    await createSponsorRelationship(referrerId, newUserId);\n\n    // Update cached tree counts for affected users\n    await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n\n    return optimalPosition.side;\n\n  } catch (error) {\n    console.error('Binary tree placement error:', error);\n    throw error;\n  }\n}\n\n// Create sponsor relationship (separate from binary placement)\nasync function createSponsorRelationship(sponsorId: string, newUserId: string): Promise<void> {\n  try {\n    // Update the new user's referrerId field to track sponsor\n    await prisma.user.update({\n      where: { id: newUserId },\n      data: { referrerId: sponsorId },\n    });\n\n    // Update sponsor's direct referral count\n    await prisma.user.update({\n      where: { id: sponsorId },\n      data: {\n        directReferralCount: { increment: 1 },\n        updatedAt: new Date(),\n      },\n    });\n\n    // Mark referral as direct sponsor if the binary placement parent is the same as sponsor\n    await prisma.referral.updateMany({\n      where: {\n        referrerId: sponsorId,\n        referredId: newUserId,\n      },\n      data: {\n        isDirectSponsor: true,\n      },\n    });\n\n  } catch (error) {\n    console.error('Sponsor relationship creation error:', error);\n    // Don't throw error as this is supplementary to binary placement\n  }\n}\n\n// Update cached downline counts for a user\nexport async function updateCachedDownlineCounts(userId: string): Promise<void> {\n  try {\n    const leftCount = await calculateDownlineCount(userId, 'LEFT');\n    const rightCount = await calculateDownlineCount(userId, 'RIGHT');\n\n    await prisma.user.update({\n      where: { id: userId },\n      data: {\n        totalLeftDownline: leftCount,\n        totalRightDownline: rightCount,\n        lastTreeUpdate: new Date(),\n      },\n    });\n  } catch (error) {\n    console.error('Update cached downline counts error:', error);\n  }\n}\n\n// Get cached downline counts (with fallback to real-time calculation)\nexport async function getCachedDownlineCounts(userId: string): Promise<{ left: number; right: number; total: number }> {\n  try {\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      select: {\n        totalLeftDownline: true,\n        totalRightDownline: true,\n        lastTreeUpdate: true,\n      },\n    });\n\n    if (!user) {\n      return { left: 0, right: 0, total: 0 };\n    }\n\n    // Check if cache is recent (within last 30 minutes for more accurate counts)\n    const cacheAge = user.lastTreeUpdate ? Date.now() - user.lastTreeUpdate.getTime() : Infinity;\n    const cacheValidDuration = 30 * 60 * 1000; // 30 minutes\n\n    if (cacheAge < cacheValidDuration && user.totalLeftDownline !== null && user.totalRightDownline !== null) {\n      // Use cached values\n      return {\n        left: user.totalLeftDownline,\n        right: user.totalRightDownline,\n        total: user.totalLeftDownline + user.totalRightDownline,\n      };\n    } else {\n      // Cache is stale or missing, recalculate and update\n      const leftCount = await calculateDownlineCount(userId, 'LEFT');\n      const rightCount = await calculateDownlineCount(userId, 'RIGHT');\n\n      // Update cache asynchronously\n      updateCachedDownlineCounts(userId).catch(console.error);\n\n      return {\n        left: leftCount,\n        right: rightCount,\n        total: leftCount + rightCount,\n      };\n    }\n  } catch (error) {\n    console.error('Get cached downline counts error:', error);\n    return { left: 0, right: 0, total: 0 };\n  }\n}\n\n// Find optimal placement in specific side with weaker leg logic (LEGACY - for backward compatibility)\nasync function findOptimalPlacementInSide(referrerId: string, targetSide: 'LEFT' | 'RIGHT'): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' }> {\n  try {\n    // First, try to find the next available spot in the target side\n    const availableSpot = await findNextAvailableSpotInLeg(referrerId, targetSide);\n\n    if (availableSpot) {\n      return availableSpot;\n    }\n\n    // If no spot available, find the position with smallest downline in that side\n    const sideUsers = await getDownlineUsers(referrerId, targetSide);\n\n    // Find the user with the smallest total downline in the target side\n    let optimalUser = referrerId;\n    let minDownlineCount = Infinity;\n\n    for (const sideUser of sideUsers) {\n      const leftCount = await calculateDownlineCount(sideUser.id, 'LEFT');\n      const rightCount = await calculateDownlineCount(sideUser.id, 'RIGHT');\n      const totalDownline = leftCount + rightCount;\n\n      if (totalDownline < minDownlineCount) {\n        // Check if this user has available spots\n        const userReferrals = await referralDb.findByReferrerId(sideUser.id);\n        const hasLeft = userReferrals.some(r => r.placementSide === 'LEFT');\n        const hasRight = userReferrals.some(r => r.placementSide === 'RIGHT');\n\n        if (!hasLeft || !hasRight) {\n          minDownlineCount = totalDownline;\n          optimalUser = sideUser.id;\n        }\n      }\n    }\n\n    // Determine which side to place in for the optimal user\n    const optimalUserReferrals = await referralDb.findByReferrerId(optimalUser);\n    const hasLeft = optimalUserReferrals.some(r => r.placementSide === 'LEFT');\n    const hasRight = optimalUserReferrals.some(r => r.placementSide === 'RIGHT');\n\n    if (!hasLeft) {\n      return { userId: optimalUser, side: 'LEFT' };\n    } else if (!hasRight) {\n      return { userId: optimalUser, side: 'RIGHT' };\n    }\n\n    // If both sides occupied, use weaker leg logic\n    const leftCount = await calculateDownlineCount(optimalUser, 'LEFT');\n    const rightCount = await calculateDownlineCount(optimalUser, 'RIGHT');\n    const weakerSide: 'LEFT' | 'RIGHT' = leftCount <= rightCount ? 'LEFT' : 'RIGHT';\n\n    return { userId: optimalUser, side: weakerSide };\n\n  } catch (error) {\n    console.error('Optimal placement in side error:', error);\n    return { userId: referrerId, side: targetSide };\n  }\n}\n\n// NEW: Find deepest available position in LEFT side only (strict left-side placement)\nasync function findDeepestLeftPosition(referrerId: string): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' }> {\n  try {\n    // Start from the referrer and traverse down the LEFT side only\n    let currentUserId = referrerId;\n    let currentLevel = 0;\n    const maxDepth = 20; // Prevent infinite loops\n\n    while (currentLevel < maxDepth) {\n      // Verify current user exists\n      const userExists = await prisma.user.findUnique({\n        where: { id: currentUserId },\n        select: { id: true },\n      });\n\n      if (!userExists) {\n        // User doesn't exist, fallback to referrer\n        return { userId: referrerId, side: 'LEFT' };\n      }\n\n      // Check if current user has a LEFT spot available\n      const currentReferrals = await referralDb.findByReferrerId(currentUserId);\n      const hasLeft = currentReferrals.some(r => r.placementSide === 'LEFT');\n\n      if (!hasLeft) {\n        // Found an available LEFT spot\n        return { userId: currentUserId, side: 'LEFT' };\n      }\n\n      // Move to the LEFT child and continue traversing\n      const leftChild = currentReferrals.find(r => r.placementSide === 'LEFT');\n      if (!leftChild) {\n        // This shouldn't happen if hasLeft is true, but safety check\n        return { userId: currentUserId, side: 'LEFT' };\n      }\n\n      currentUserId = leftChild.referredId;\n      currentLevel++;\n    }\n\n    // If we've reached max depth, place at the last position\n    return { userId: currentUserId, side: 'LEFT' };\n\n  } catch (error) {\n    console.error('Find deepest left position error:', error);\n    return { userId: referrerId, side: 'LEFT' };\n  }\n}\n\n// NEW: Find deepest available position in RIGHT side only (strict right-side placement)\nasync function findDeepestRightPosition(referrerId: string): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' }> {\n  try {\n    // Start from the referrer and traverse down the RIGHT side only\n    let currentUserId = referrerId;\n    let currentLevel = 0;\n    const maxDepth = 20; // Prevent infinite loops\n\n    while (currentLevel < maxDepth) {\n      // Verify current user exists\n      const userExists = await prisma.user.findUnique({\n        where: { id: currentUserId },\n        select: { id: true },\n      });\n\n      if (!userExists) {\n        // User doesn't exist, fallback to referrer\n        return { userId: referrerId, side: 'RIGHT' };\n      }\n\n      // Check if current user has a RIGHT spot available\n      const currentReferrals = await referralDb.findByReferrerId(currentUserId);\n      const hasRight = currentReferrals.some(r => r.placementSide === 'RIGHT');\n\n      if (!hasRight) {\n        // Found an available RIGHT spot\n        return { userId: currentUserId, side: 'RIGHT' };\n      }\n\n      // Move to the RIGHT child and continue traversing\n      const rightChild = currentReferrals.find(r => r.placementSide === 'RIGHT');\n      if (!rightChild) {\n        // This shouldn't happen if hasRight is true, but safety check\n        return { userId: currentUserId, side: 'RIGHT' };\n      }\n\n      currentUserId = rightChild.referredId;\n      currentLevel++;\n    }\n\n    // If we've reached max depth, place at the last position\n    return { userId: currentUserId, side: 'RIGHT' };\n\n  } catch (error) {\n    console.error('Find deepest right position error:', error);\n    return { userId: referrerId, side: 'RIGHT' };\n  }\n}\n\n// Enhanced place user in specific side with weaker leg algorithm (LEGACY - for backward compatibility)\nexport async function placeUserInSpecificSide(referrerId: string, newUserId: string, side: 'LEFT' | 'RIGHT'): Promise<'LEFT' | 'RIGHT'> {\n  try {\n    // Find optimal placement position within the specified side\n    const optimalPosition = await findOptimalPlacementInSide(referrerId, side);\n\n    // Create referral relationship with the optimal parent\n    await referralDb.create({\n      referrerId: optimalPosition.userId,\n      referredId: newUserId,\n      placementSide: optimalPosition.side,\n    });\n\n    // Update the parent's referral ID\n    const updateData = optimalPosition.side === 'LEFT'\n      ? { leftReferralId: newUserId }\n      : { rightReferralId: newUserId };\n\n    await prisma.user.update({\n      where: { id: optimalPosition.userId },\n      data: updateData,\n    });\n\n    // Create sponsor relationship (separate from binary placement)\n    await createSponsorRelationship(referrerId, newUserId);\n\n    // Update cached tree counts for affected users\n    await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n\n    return optimalPosition.side;\n\n  } catch (error) {\n    console.error('Specific side placement error:', error);\n    throw error;\n  }\n}\n\n// NEW: Place user strictly in LEFT side only (deepest available left position)\nexport async function placeUserInLeftSideOnly(referrerId: string, newUserId: string): Promise<'LEFT' | 'RIGHT'> {\n  try {\n    // Find the deepest available position in the LEFT side\n    const optimalPosition = await findDeepestLeftPosition(referrerId);\n\n    // Create referral relationship with the optimal parent\n    await referralDb.create({\n      referrerId: optimalPosition.userId,\n      referredId: newUserId,\n      placementSide: optimalPosition.side,\n    });\n\n    // Update the parent's left referral ID\n    await prisma.user.update({\n      where: { id: optimalPosition.userId },\n      data: { leftReferralId: newUserId },\n    });\n\n    // Create sponsor relationship (separate from binary placement)\n    await createSponsorRelationship(referrerId, newUserId);\n\n    // Update cached tree counts for affected users\n    await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n\n    return optimalPosition.side;\n\n  } catch (error) {\n    console.error('Left side only placement error:', error);\n    throw error;\n  }\n}\n\n// NEW: Place user strictly in RIGHT side only (deepest available right position)\nexport async function placeUserInRightSideOnly(referrerId: string, newUserId: string): Promise<'LEFT' | 'RIGHT'> {\n  try {\n    // Find the deepest available position in the RIGHT side\n    const optimalPosition = await findDeepestRightPosition(referrerId);\n\n    // Create referral relationship with the optimal parent\n    await referralDb.create({\n      referrerId: optimalPosition.userId,\n      referredId: newUserId,\n      placementSide: optimalPosition.side,\n    });\n\n    // Update the parent's right referral ID\n    await prisma.user.update({\n      where: { id: optimalPosition.userId },\n      data: { rightReferralId: newUserId },\n    });\n\n    // Create sponsor relationship (separate from binary placement)\n    await createSponsorRelationship(referrerId, newUserId);\n\n    // Update cached tree counts for affected users\n    await updateTreeCountsAfterPlacement(referrerId, optimalPosition.userId);\n\n    return optimalPosition.side;\n\n  } catch (error) {\n    console.error('Right side only placement error:', error);\n    throw error;\n  }\n}\n\n// NEW: Main placement function that routes to appropriate algorithm based on referral link type\nexport async function placeUserByReferralType(\n  referrerId: string,\n  newUserId: string,\n  referralType: 'general' | 'left' | 'right'\n): Promise<'LEFT' | 'RIGHT'> {\n  try {\n    switch (referralType) {\n      case 'left':\n        // Strict left-side placement: find deepest available left position\n        return await placeUserInLeftSideOnly(referrerId, newUserId);\n\n      case 'right':\n        // Strict right-side placement: find deepest available right position\n        return await placeUserInRightSideOnly(referrerId, newUserId);\n\n      case 'general':\n      default:\n        // Default weaker leg placement\n        return await placeUserInBinaryTree(referrerId, newUserId);\n    }\n  } catch (error) {\n    console.error('Placement by referral type error:', error);\n    throw error;\n  }\n}\n\n// Find next available spot in a specific leg\nasync function findNextAvailableSpotInLeg(rootUserId: string, targetSide: 'LEFT' | 'RIGHT'): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' } | null> {\n  try {\n    // Get the first user in the target leg\n    const rootReferrals = await referralDb.findByReferrerId(rootUserId);\n    const firstInLeg = rootReferrals.find(r => r.placementSide === targetSide);\n\n    if (!firstInLeg) {\n      // The target side is completely empty\n      return { userId: rootUserId, side: targetSide };\n    }\n\n    // Traverse down the leg to find the first available spot\n    const queue = [firstInLeg.referredId];\n\n    while (queue.length > 0) {\n      const currentUserId = queue.shift()!;\n      const currentReferrals = await referralDb.findByReferrerId(currentUserId);\n\n      // Check if this user has any empty spots\n      const hasLeft = currentReferrals.some(r => r.placementSide === 'LEFT');\n      const hasRight = currentReferrals.some(r => r.placementSide === 'RIGHT');\n\n      if (!hasLeft) {\n        return { userId: currentUserId, side: 'LEFT' };\n      }\n      if (!hasRight) {\n        return { userId: currentUserId, side: 'RIGHT' };\n      }\n\n      // Add children to queue for further traversal\n      currentReferrals.forEach(r => {\n        queue.push(r.referredId);\n      });\n    }\n\n    return null; // No available spot found\n  } catch (error) {\n    console.error('Find available spot error:', error);\n    return null;\n  }\n}\n\n// Process direct referral bonus (10% of investment) - Added directly to sponsor's wallet\n// ONLY active sponsors (with active mining units) receive commissions\n// ONLY paid ONCE per user (first purchase only)\nexport async function processDirectReferralBonus(referrerId: string, investmentAmount: number, purchaserId?: string) {\n  try {\n    // Check if sponsor is active (has active mining units)\n    const isActive = await hasActiveMiningUnits(referrerId);\n\n    if (!isActive) {\n      console.log(`Skipping direct referral bonus for inactive sponsor ${referrerId} - no active mining units`);\n      return 0; // Return 0 commission for inactive sponsors\n    }\n\n    // Check if this user has already received first commission from this purchaser\n    if (purchaserId) {\n      const purchaser = await prisma.user.findUnique({\n        where: { id: purchaserId },\n        select: { hasReceivedFirstCommission: true, firstName: true, lastName: true }\n      });\n\n      if (purchaser?.hasReceivedFirstCommission) {\n        console.log(`Skipping direct referral bonus - sponsor ${referrerId} already received first commission from user ${purchaserId} (${purchaser.firstName} ${purchaser.lastName})`);\n        return 0; // Return 0 commission for subsequent purchases\n      }\n    }\n\n    const bonusPercentage = parseFloat(await adminSettingsDb.get('DIRECT_REFERRAL_BONUS') || '10');\n    const bonusAmount = (investmentAmount * bonusPercentage) / 100;\n\n    // Add commission directly to sponsor's wallet balance\n    const sponsorWallet = await walletBalanceDb.getOrCreate(referrerId);\n    await walletBalanceDb.updateBalance(referrerId, {\n      availableBalance: sponsorWallet.availableBalance + bonusAmount,\n    });\n\n    // Create direct referral transaction with reference to the purchaser\n    await transactionDb.create({\n      userId: referrerId,\n      type: 'DIRECT_REFERRAL',\n      amount: bonusAmount,\n      description: `Direct referral bonus (${bonusPercentage}% of $${investmentAmount}) - First purchase`,\n      reference: purchaserId ? `from_user:${purchaserId}` : 'direct_referral',\n      status: 'COMPLETED',\n    });\n\n    // Update referral commission earned\n    await prisma.referral.updateMany({\n      where: {\n        referrerId,\n        referred: {\n          miningUnits: {\n            some: {\n              investmentAmount,\n            },\n          },\n        },\n      },\n      data: {\n        commissionEarned: {\n          increment: bonusAmount,\n        },\n      },\n    });\n\n    // Mark the purchaser as having received their first commission\n    if (purchaserId) {\n      await prisma.user.update({\n        where: { id: purchaserId },\n        data: { hasReceivedFirstCommission: true }\n      });\n    }\n\n    console.log(`First-time direct referral bonus of $${bonusAmount} awarded to active sponsor ${referrerId} from user ${purchaserId}`);\n    return bonusAmount;\n\n  } catch (error) {\n    console.error('Direct referral bonus error:', error);\n    throw error;\n  }\n}\n\n// Add points to binary system when someone makes an investment ($100 = 1 point)\nexport async function addBinaryPoints(userId: string, investmentAmount: number) {\n  try {\n    // Calculate points: $100 investment = 1 point (with 2 decimal precision)\n    // $150 = 1.5 points, $250 = 2.5 points, etc.\n    const points = Math.round((investmentAmount / 100) * 100) / 100; // Round to 2 decimal places\n\n    if (points <= 0) return; // No points to add if investment is less than $100\n\n    // Find all upline users and add points to their binary system (ONLY active upliners)\n    const uplineUsers = await getUplineUsers(userId);\n\n    for (const uplineUser of uplineUsers) {\n      // Check if upline user is active (has active mining units)\n      const isActive = await hasActiveMiningUnits(uplineUser.id);\n\n      if (!isActive) {\n        console.log(`Skipping inactive user ${uplineUser.id} - no active mining units`);\n        continue; // Skip inactive users\n      }\n\n      // Determine which side this user is on relative to upline\n      const placementSide = await getUserPlacementSide(uplineUser.id, userId);\n\n      if (placementSide) {\n        // Get current binary points for this user\n        const currentBinaryPoints = await binaryPointsDb.findByUserId(uplineUser.id);\n\n        // Get max points per side setting\n        const maxPointsPerSide = parseFloat(await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '2000');\n\n        // Check current points on the target side\n        const currentLeftPoints = currentBinaryPoints?.leftPoints || 0;\n        const currentRightPoints = currentBinaryPoints?.rightPoints || 0;\n\n        let pointsToAdd = 0;\n        let sideToUpdate: 'LEFT' | 'RIGHT' = placementSide;\n\n        if (placementSide === 'LEFT') {\n          // Check if left side has reached the maximum\n          if (currentLeftPoints >= maxPointsPerSide) {\n            console.log(`User ${uplineUser.id} left side has reached maximum (${currentLeftPoints}/${maxPointsPerSide}). No points added.`);\n            continue; // Skip adding points to this user\n          }\n\n          // Calculate how many points can be added without exceeding the limit\n          pointsToAdd = Math.min(points, maxPointsPerSide - currentLeftPoints);\n        } else {\n          // Check if right side has reached the maximum\n          if (currentRightPoints >= maxPointsPerSide) {\n            console.log(`User ${uplineUser.id} right side has reached maximum (${currentRightPoints}/${maxPointsPerSide}). No points added.`);\n            continue; // Skip adding points to this user\n          }\n\n          // Calculate how many points can be added without exceeding the limit\n          pointsToAdd = Math.min(points, maxPointsPerSide - currentRightPoints);\n        }\n\n        // Only add points if there's room\n        if (pointsToAdd > 0) {\n          const updateData = placementSide === 'LEFT'\n            ? { leftPoints: pointsToAdd }\n            : { rightPoints: pointsToAdd };\n\n          await binaryPointsDb.upsert({\n            userId: uplineUser.id,\n            ...updateData,\n          });\n\n          console.log(`Added ${pointsToAdd} points to ${placementSide} side for active user ${uplineUser.id} (${pointsToAdd < points ? 'capped at limit' : 'full amount'})`);\n        }\n      }\n    }\n\n  } catch (error) {\n    console.error('Binary points addition error:', error);\n    throw error;\n  }\n}\n\n// Get all upline users for a given user\nasync function getUplineUsers(userId: string): Promise<Array<{ id: string; email: string }>> {\n  try {\n    const uplineUsers = [];\n    let currentUserId = userId;\n\n    // Traverse up the tree (maximum 10 levels to prevent infinite loops)\n    for (let level = 0; level < 10; level++) {\n      const referral = await prisma.referral.findFirst({\n        where: { referredId: currentUserId },\n        include: {\n          referrer: {\n            select: { id: true, email: true },\n          },\n        },\n      });\n\n      if (!referral) break;\n\n      uplineUsers.push(referral.referrer);\n      currentUserId = referral.referrerId;\n    }\n\n    return uplineUsers;\n\n  } catch (error) {\n    console.error('Upline users fetch error:', error);\n    return [];\n  }\n}\n\n// Get all ACTIVE upline users for a given user (skip inactive users)\nasync function getActiveUplineUsers(userId: string): Promise<Array<{ id: string; email: string; isActive: boolean }>> {\n  try {\n    const uplineUsers = [];\n    let currentUserId = userId;\n\n    // Traverse up the tree (maximum 10 levels to prevent infinite loops)\n    for (let level = 0; level < 10; level++) {\n      const referral = await prisma.referral.findFirst({\n        where: { referredId: currentUserId },\n        include: {\n          referrer: {\n            select: { id: true, email: true, isActive: true },\n          },\n        },\n      });\n\n      if (!referral) break;\n\n      // Only add active users to the list\n      if (referral.referrer.isActive) {\n        uplineUsers.push(referral.referrer);\n      }\n\n      // Continue traversing up regardless of active status\n      currentUserId = referral.referrerId;\n    }\n\n    return uplineUsers;\n\n  } catch (error) {\n    console.error('Active upline users fetch error:', error);\n    return [];\n  }\n}\n\n// Determine which side a user is on relative to an upline user\nasync function getUserPlacementSide(uplineUserId: string, userId: string): Promise<'LEFT' | 'RIGHT' | null> {\n  try {\n    // Check direct placement first\n    const directReferral = await prisma.referral.findFirst({\n      where: {\n        referrerId: uplineUserId,\n        referredId: userId,\n      },\n    });\n    \n    if (directReferral) {\n      return directReferral.placementSide;\n    }\n    \n    // Check indirect placement by traversing down the tree\n    const leftSideUsers = await getDownlineUsers(uplineUserId, 'LEFT');\n    const rightSideUsers = await getDownlineUsers(uplineUserId, 'RIGHT');\n    \n    if (leftSideUsers.some(u => u.id === userId)) {\n      return 'LEFT';\n    }\n    \n    if (rightSideUsers.some(u => u.id === userId)) {\n      return 'RIGHT';\n    }\n    \n    return null;\n    \n  } catch (error) {\n    console.error('Placement side determination error:', error);\n    return null;\n  }\n}\n\n// Get all downline users for a specific side\nasync function getDownlineUsers(userId: string, side: 'LEFT' | 'RIGHT'): Promise<Array<{ id: string }>> {\n  try {\n    const downlineUsers = [];\n    const visited = new Set<string>();\n\n    // Start with the direct placement on the specified side\n    const initialReferrals = await prisma.referral.findMany({\n      where: {\n        referrerId: userId,\n        placementSide: side,\n      },\n      select: {\n        referredId: true,\n      },\n    });\n\n    // Use BFS to traverse the entire subtree\n    const queue = initialReferrals.map(r => r.referredId);\n\n    while (queue.length > 0) {\n      const currentUserId = queue.shift()!;\n\n      // Skip if already visited (prevent infinite loops)\n      if (visited.has(currentUserId)) continue;\n      visited.add(currentUserId);\n\n      // Add current user to downline\n      downlineUsers.push({ id: currentUserId });\n\n      // Get all referrals (both LEFT and RIGHT) from current user\n      const referrals = await prisma.referral.findMany({\n        where: {\n          referrerId: currentUserId,\n        },\n        select: {\n          referredId: true,\n        },\n      });\n\n      // Add all children to queue for further traversal\n      for (const referral of referrals) {\n        if (!visited.has(referral.referredId)) {\n          queue.push(referral.referredId);\n        }\n      }\n    }\n\n    return downlineUsers;\n\n  } catch (error) {\n    console.error('Downline users fetch error:', error);\n    return [];\n  }\n}\n\n// Get all downline users (both sides combined) for total team count\nasync function getAllDownlineUsers(userId: string): Promise<Array<{ id: string; isActive: boolean }>> {\n  try {\n    const downlineUsers = [];\n    const visited = new Set<string>();\n\n    // Get all direct referrals (both LEFT and RIGHT)\n    const initialReferrals = await prisma.referral.findMany({\n      where: {\n        referrerId: userId,\n      },\n      select: {\n        referredId: true,\n      },\n    });\n\n    // Use BFS to traverse the entire binary tree\n    const queue = initialReferrals.map(r => r.referredId);\n\n    while (queue.length > 0) {\n      const currentUserId = queue.shift()!;\n\n      // Skip if already visited (prevent infinite loops)\n      if (visited.has(currentUserId)) continue;\n      visited.add(currentUserId);\n\n      // Get user info including active status\n      const user = await prisma.user.findUnique({\n        where: { id: currentUserId },\n        select: { id: true, isActive: true },\n      });\n\n      if (user) {\n        downlineUsers.push({ id: user.id, isActive: user.isActive });\n\n        // Get all referrals from current user\n        const referrals = await prisma.referral.findMany({\n          where: {\n            referrerId: currentUserId,\n          },\n          select: {\n            referredId: true,\n          },\n        });\n\n        // Add all children to queue for further traversal\n        for (const referral of referrals) {\n          if (!visited.has(referral.referredId)) {\n            queue.push(referral.referredId);\n          }\n        }\n      }\n    }\n\n    return downlineUsers;\n\n  } catch (error) {\n    console.error('All downline users fetch error:', error);\n    return [];\n  }\n}\n\n// Process weekly binary matching (15:00 UTC on Saturdays)\nexport async function processBinaryMatching() {\n  try {\n    console.log('Starting binary matching process...');\n\n    const maxPointsPerSide = parseFloat(await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '2000');\n    const pointValue = parseFloat(await adminSettingsDb.get('BINARY_POINT_VALUE') || '10'); // Dynamic point value from settings\n    \n    // Get all users with binary points\n    const usersWithPoints = await prisma.binaryPoints.findMany({\n      where: {\n        OR: [\n          { leftPoints: { gt: 0 } },\n          { rightPoints: { gt: 0 } },\n        ],\n      },\n      include: {\n        user: {\n          select: { id: true, email: true },\n        },\n      },\n    });\n    \n    console.log(`Processing binary matching for ${usersWithPoints.length} users`);\n\n    const matchingResults = [];\n\n    for (const userPoints of usersWithPoints) {\n      try {\n        // Calculate matching points (minimum of left and right, capped at max per side)\n        const leftPoints = Math.min(userPoints.leftPoints, maxPointsPerSide);\n        const rightPoints = Math.min(userPoints.rightPoints, maxPointsPerSide);\n        const matchedPoints = Math.min(leftPoints, rightPoints);\n\n        if (matchedPoints > 0) {\n          // Calculate direct payout: 1 point = $10\n          const userPayout = matchedPoints * pointValue;\n\n          try {\n            // Create binary bonus transaction\n            await transactionDb.create({\n              userId: userPoints.userId,\n              type: 'BINARY_BONUS',\n              amount: userPayout,\n              description: `Binary matching bonus - ${matchedPoints} points matched at $${pointValue} per point`,\n              status: 'COMPLETED',\n            });\n\n            // Add to wallet balance\n            await walletBalanceDb.addEarnings(userPoints.userId, userPayout);\n\n            // Calculate remaining points after matching - reset weaker side to 0\n            // Example: User has 7 left, 5 right -> 5 matched, left becomes 2, right becomes 0\n            const remainingLeftPoints = Math.max(0, userPoints.leftPoints - matchedPoints);\n            const remainingRightPoints = Math.max(0, userPoints.rightPoints - matchedPoints);\n\n            // Reset the weaker side to 0 after matching (proper binary matching rule)\n            const finalLeftPoints = userPoints.leftPoints > userPoints.rightPoints ? remainingLeftPoints : 0;\n            const finalRightPoints = userPoints.rightPoints > userPoints.leftPoints ? remainingRightPoints : 0;\n\n            // Update binary points - reset weaker side to 0, keep stronger side remainder\n            await prisma.binaryPoints.update({\n              where: { id: userPoints.id },\n              data: {\n                leftPoints: finalLeftPoints, // Reset weaker side to 0\n                rightPoints: finalRightPoints, // Reset weaker side to 0\n                matchedPoints: { increment: matchedPoints },\n                totalMatched: { increment: matchedPoints }, // Track lifetime total\n                lastMatchDate: new Date(), // Track when points were last matched\n                flushDate: new Date(), // Track when points were processed\n              },\n            });\n\n            matchingResults.push({\n              userId: userPoints.userId,\n              matchedPoints,\n              payout: userPayout,\n              remainingLeftPoints: finalLeftPoints,\n              remainingRightPoints: finalRightPoints,\n            });\n\n            console.log(`User ${userPoints.userId}: ${matchedPoints} points matched, $${userPayout.toFixed(2)} payout, remaining: L${finalLeftPoints} R${finalRightPoints}`);\n          } catch (payoutError) {\n            console.error(`Error processing payout for user ${userPoints.userId}:`, payoutError);\n            // Continue with next user instead of failing the entire process\n          }\n        } else {\n          // No matching possible, but still reset excess points if over the limit\n          const excessLeft = Math.max(0, userPoints.leftPoints - maxPointsPerSide);\n          const excessRight = Math.max(0, userPoints.rightPoints - maxPointsPerSide);\n\n          if (excessLeft > 0 || excessRight > 0) {\n            try {\n              // Reset excess points (pressure out)\n              await prisma.binaryPoints.update({\n                where: { id: userPoints.id },\n                data: {\n                  leftPoints: Math.min(userPoints.leftPoints, maxPointsPerSide),\n                  rightPoints: Math.min(userPoints.rightPoints, maxPointsPerSide),\n                  flushDate: new Date(),\n                },\n              });\n\n              console.log(`User ${userPoints.userId}: Excess points reset - L${excessLeft} R${excessRight} points flushed`);\n            } catch (flushError) {\n              console.error(`Error flushing excess points for user ${userPoints.userId}:`, flushError);\n            }\n          }\n        }\n        \n      } catch (userError) {\n        console.error(`Error processing binary matching for user ${userPoints.userId}:`, userError);\n      }\n    }\n    \n    // Log the binary matching process\n    await systemLogDb.create({\n      action: 'BINARY_MATCHING_PROCESSED',\n      details: {\n        usersProcessed: usersWithPoints.length,\n        totalMatchedPoints: matchingResults.reduce((sum, r) => sum + r.matchedPoints, 0),\n        pointValue,\n        totalPayouts: matchingResults.reduce((sum, r) => sum + r.payout, 0),\n        timestamp: new Date().toISOString(),\n      },\n    });\n\n    console.log(`Binary matching completed. Processed ${matchingResults.length} users with total payouts: $${matchingResults.reduce((sum, r) => sum + r.payout, 0).toFixed(2)}`);\n\n    return {\n      success: true,\n      usersProcessed: matchingResults.length,\n      totalPayouts: matchingResults.reduce((sum, r) => sum + r.payout, 0),\n      matchingResults,\n    };\n    \n  } catch (error) {\n    console.error('Binary matching process error:', error);\n    throw error;\n  }\n}\n\n// Get sponsor information for a user\nexport async function getSponsorInfo(userId: string): Promise<{ id: string; email: string; firstName: string; lastName: string } | null> {\n  try {\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      select: { referrerId: true },\n    });\n\n    if (!user?.referrerId) return null;\n\n    const sponsor = await prisma.user.findUnique({\n      where: { id: user.referrerId },\n      select: {\n        id: true,\n        email: true,\n        firstName: true,\n        lastName: true,\n      },\n    });\n\n    return sponsor;\n  } catch (error) {\n    console.error('Sponsor info fetch error:', error);\n    return null;\n  }\n}\n\n// Get direct referral count for a user (sponsored users)\nexport async function getDirectReferralCount(userId: string): Promise<number> {\n  try {\n    const count = await prisma.user.count({\n      where: { referrerId: userId },\n    });\n    return count;\n  } catch (error) {\n    console.error('Direct referral count error:', error);\n    return 0;\n  }\n}\n\n// Get total team count (all downline users in binary tree) - uses cached values\nexport async function getTotalTeamCount(userId: string): Promise<{ left: number; right: number; total: number }> {\n  try {\n    return await getCachedDownlineCounts(userId);\n  } catch (error) {\n    console.error('Total team count error:', error);\n    return { left: 0, right: 0, total: 0 };\n  }\n}\n\n// Get detailed team statistics\nexport async function getDetailedTeamStats(userId: string): Promise<{\n  directReferrals: number;\n  leftTeam: number;\n  rightTeam: number;\n  totalTeam: number;\n  activeMembers: number;\n  recentJoins: number; // Last 30 days\n}> {\n  try {\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      select: { directReferralCount: true },\n    });\n\n    const teamCounts = await getCachedDownlineCounts(userId);\n\n    // Get all downline users for accurate active member count\n    const allDownlineUsers = await getAllDownlineUsers(userId);\n    const activeMembers = allDownlineUsers.filter(u => u.isActive).length;\n\n    // Get recent joins (last 30 days) - direct referrals only\n    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n    const recentJoins = await prisma.user.count({\n      where: {\n        referrerId: userId,\n        createdAt: { gte: thirtyDaysAgo },\n      },\n    });\n\n    return {\n      directReferrals: user?.directReferralCount || 0,\n      leftTeam: teamCounts.left,\n      rightTeam: teamCounts.right,\n      totalTeam: teamCounts.total,\n      activeMembers,\n      recentJoins,\n    };\n  } catch (error) {\n    console.error('Detailed team stats error:', error);\n    return {\n      directReferrals: 0,\n      leftTeam: 0,\n      rightTeam: 0,\n      totalTeam: 0,\n      activeMembers: 0,\n      recentJoins: 0,\n    };\n  }\n}\n\n// Find all users in a specific generation (level) of the tree\nexport async function getUsersByGeneration(userId: string, generation: number): Promise<Array<{\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  createdAt: Date;\n  placementSide: 'LEFT' | 'RIGHT';\n}>> {\n  try {\n    if (generation <= 0) return [];\n\n    let currentLevelUsers = [{ id: userId, side: null as 'LEFT' | 'RIGHT' | null }];\n\n    for (let level = 1; level <= generation; level++) {\n      const nextLevelUsers = [];\n\n      for (const currentUser of currentLevelUsers) {\n        const referrals = await prisma.referral.findMany({\n          where: { referrerId: currentUser.id },\n          include: {\n            referred: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                createdAt: true,\n              },\n            },\n          },\n        });\n\n        for (const referral of referrals) {\n          nextLevelUsers.push({\n            id: referral.referredId,\n            side: referral.placementSide,\n          });\n        }\n      }\n\n      currentLevelUsers = nextLevelUsers;\n    }\n\n    // Get full user details for the final generation\n    const userDetails = await Promise.all(\n      currentLevelUsers.map(async (user) => {\n        const userInfo = await prisma.user.findUnique({\n          where: { id: user.id },\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            createdAt: true,\n          },\n        });\n\n        return {\n          ...userInfo!,\n          placementSide: user.side!,\n        };\n      })\n    );\n\n    return userDetails.filter(Boolean);\n  } catch (error) {\n    console.error('Users by generation error:', error);\n    return [];\n  }\n}\n\n// Enhanced binary tree structure with detailed member information\nexport async function getBinaryTreeStructure(userId: string, depth = 3, expandedNodes: Set<string> = new Set()) {\n  try {\n    const buildTree = async (currentUserId: string, currentDepth: number, path: string = ''): Promise<any> => {\n      if (currentDepth <= 0) return null;\n\n      const user = await prisma.user.findUnique({\n        where: { id: currentUserId },\n        select: {\n          id: true,\n          email: true,\n          firstName: true,\n          lastName: true,\n          createdAt: true,\n        },\n      });\n\n      if (!user) return null;\n\n      // Check if user has active mining units (for binary tree display)\n      const isActive = await hasActiveMiningUnits(currentUserId);\n\n      // Get sponsor information\n      const sponsorInfo = await getSponsorInfo(currentUserId);\n\n      // Get direct referral count\n      const directReferralCount = await getDirectReferralCount(currentUserId);\n\n      // Get team counts\n      const teamCounts = await getTotalTeamCount(currentUserId);\n\n      // Get direct referrals (binary placement)\n      const leftReferral = await prisma.referral.findFirst({\n        where: {\n          referrerId: currentUserId,\n          placementSide: 'LEFT',\n        },\n        include: {\n          referred: {\n            select: { id: true, email: true, firstName: true, lastName: true, createdAt: true },\n          },\n        },\n      });\n\n      const rightReferral = await prisma.referral.findFirst({\n        where: {\n          referrerId: currentUserId,\n          placementSide: 'RIGHT',\n        },\n        include: {\n          referred: {\n            select: { id: true, email: true, firstName: true, lastName: true, createdAt: true },\n          },\n        },\n      });\n\n      // Get binary points\n      const binaryPoints = await binaryPointsDb.findByUserId(currentUserId);\n\n      // Determine if we should load children for infinite depth support\n      // Load children if we have remaining depth AND either:\n      // 1. We're within the initial depth (first 3 levels) - always show first 3 levels\n      // 2. OR this node is explicitly expanded - show children of expanded nodes\n      const isWithinInitialDepth = path.length < 3; // First 3 levels (root = 0, level 1 = 1 char, level 2 = 2 chars)\n      const isNodeExpanded = expandedNodes.has(currentUserId);\n\n      const shouldLoadChildren = currentDepth > 1 && (isWithinInitialDepth || isNodeExpanded);\n\n      // Check if children exist (for showing expand button)\n      const hasLeftChild = leftReferral !== null;\n      const hasRightChild = rightReferral !== null;\n\n      return {\n        user: { ...user, isActive }, // Add computed isActive status\n        sponsorInfo,\n        directReferralCount,\n        teamCounts,\n        binaryPoints: binaryPoints || { leftPoints: 0, rightPoints: 0, matchedPoints: 0 },\n        hasLeftChild,\n        hasRightChild,\n        leftChild: shouldLoadChildren && leftReferral ?\n          await buildTree(leftReferral.referredId, currentDepth - 1, path + 'L') : null,\n        rightChild: shouldLoadChildren && rightReferral ?\n          await buildTree(rightReferral.referredId, currentDepth - 1, path + 'R') : null,\n      };\n    };\n\n    return await buildTree(userId, depth);\n\n  } catch (error) {\n    console.error('Binary tree structure error:', error);\n    throw error;\n  }\n}\n\n// Load children for a specific node (for dynamic expansion)\nexport async function loadNodeChildren(userId: string): Promise<{\n  leftChild: any | null;\n  rightChild: any | null;\n}> {\n  try {\n    // Get direct referrals (binary placement)\n    const leftReferral = await prisma.referral.findFirst({\n      where: {\n        referrerId: userId,\n        placementSide: 'LEFT',\n      },\n      include: {\n        referred: {\n          select: { id: true, email: true, firstName: true, lastName: true, createdAt: true },\n        },\n      },\n    });\n\n    const rightReferral = await prisma.referral.findFirst({\n      where: {\n        referrerId: userId,\n        placementSide: 'RIGHT',\n      },\n      include: {\n        referred: {\n          select: { id: true, email: true, firstName: true, lastName: true, createdAt: true },\n        },\n      },\n    });\n\n    const buildChildNode = async (referral: any) => {\n      if (!referral) return null;\n\n      const childUserId = referral.referredId;\n\n      // Check if user has active mining units (for binary tree display)\n      const isActive = await hasActiveMiningUnits(childUserId);\n\n      // Get sponsor information\n      const sponsorInfo = await getSponsorInfo(childUserId);\n\n      // Get direct referral count\n      const directReferralCount = await getDirectReferralCount(childUserId);\n\n      // Get team counts\n      const teamCounts = await getTotalTeamCount(childUserId);\n\n      // Get binary points\n      const binaryPoints = await binaryPointsDb.findByUserId(childUserId);\n\n      // Check if this child has its own children\n      const hasLeftChild = await prisma.referral.findFirst({\n        where: { referrerId: childUserId, placementSide: 'LEFT' },\n        select: { id: true }\n      }) !== null;\n\n      const hasRightChild = await prisma.referral.findFirst({\n        where: { referrerId: childUserId, placementSide: 'RIGHT' },\n        select: { id: true }\n      }) !== null;\n\n      return {\n        user: { ...referral.referred, isActive }, // Add computed isActive status\n        sponsorInfo,\n        directReferralCount,\n        teamCounts,\n        binaryPoints: binaryPoints || { leftPoints: 0, rightPoints: 0, matchedPoints: 0 },\n        hasLeftChild,\n        hasRightChild,\n        leftChild: null, // Will be loaded on demand\n        rightChild: null, // Will be loaded on demand\n      };\n    };\n\n    const leftChild = await buildChildNode(leftReferral);\n    const rightChild = await buildChildNode(rightReferral);\n\n    return { leftChild, rightChild };\n\n  } catch (error) {\n    console.error('Load node children error:', error);\n    return { leftChild: null, rightChild: null };\n  }\n}\n\n// Search for users in the binary tree\nexport async function searchUsersInTree(rootUserId: string, searchTerm: string, maxResults = 20): Promise<Array<{\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  createdAt: Date;\n  placementPath: string; // e.g., \"L-R-L\" showing path from root\n  generation: number;\n  sponsorInfo?: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n  };\n}>> {\n  try {\n    const searchPattern = `%${searchTerm.toLowerCase()}%`;\n\n    // Get all downline users that match the search term\n    const leftUsers = await getDownlineUsers(rootUserId, 'LEFT');\n    const rightUsers = await getDownlineUsers(rootUserId, 'RIGHT');\n    const allDownlineIds = [...leftUsers, ...rightUsers].map(u => u.id);\n\n    if (allDownlineIds.length === 0) return [];\n\n    const matchingUsers = await prisma.user.findMany({\n      where: {\n        id: { in: allDownlineIds },\n        OR: [\n          { email: { contains: searchTerm, mode: 'insensitive' } },\n          { firstName: { contains: searchTerm, mode: 'insensitive' } },\n          { lastName: { contains: searchTerm, mode: 'insensitive' } },\n        ],\n      },\n      select: {\n        id: true,\n        email: true,\n        firstName: true,\n        lastName: true,\n        createdAt: true,\n        referrerId: true,\n      },\n      take: maxResults,\n    });\n\n    // Get placement path and sponsor info for each user\n    const results = await Promise.all(\n      matchingUsers.map(async (user) => {\n        const placementPath = await getPlacementPath(rootUserId, user.id);\n        const generation = placementPath.split('-').length;\n\n        let sponsorInfo = undefined;\n        if (user.referrerId) {\n          sponsorInfo = await prisma.user.findUnique({\n            where: { id: user.referrerId },\n            select: {\n              id: true,\n              email: true,\n              firstName: true,\n              lastName: true,\n            },\n          });\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          createdAt: user.createdAt,\n          placementPath,\n          generation,\n          sponsorInfo: sponsorInfo || undefined,\n        };\n      })\n    );\n\n    return results;\n  } catch (error) {\n    console.error('Search users in tree error:', error);\n    return [];\n  }\n}\n\n// Get placement path from root to a specific user (e.g., \"L-R-L\")\nasync function getPlacementPath(rootUserId: string, targetUserId: string): Promise<string> {\n  try {\n    if (rootUserId === targetUserId) return 'ROOT';\n\n    const path: string[] = [];\n    let currentUserId = targetUserId;\n\n    // Traverse up the tree to find path\n    while (currentUserId !== rootUserId) {\n      const referral = await prisma.referral.findFirst({\n        where: { referredId: currentUserId },\n      });\n\n      if (!referral) break;\n\n      path.unshift(referral.placementSide === 'LEFT' ? 'L' : 'R');\n      currentUserId = referral.referrerId;\n\n      // Prevent infinite loops\n      if (path.length > 20) break;\n    }\n\n    return path.join('-') || 'UNKNOWN';\n  } catch (error) {\n    console.error('Get placement path error:', error);\n    return 'UNKNOWN';\n  }\n}\n\n// Update tree counts after a new user placement\nasync function updateTreeCountsAfterPlacement(sponsorId: string, placementParentId: string): Promise<void> {\n  try {\n    // Update counts for the sponsor (if different from placement parent)\n    if (sponsorId !== placementParentId) {\n      await updateCachedDownlineCounts(sponsorId);\n    }\n\n    // Update counts for the placement parent\n    await updateCachedDownlineCounts(placementParentId);\n\n    // Update counts for all upline users from the placement parent\n    const uplineUsers = await getUplineUsers(placementParentId);\n    const updatePromises = uplineUsers.map(user => updateCachedDownlineCounts(user.id));\n    await Promise.all(updatePromises);\n\n  } catch (error) {\n    console.error('Update tree counts after placement error:', error);\n    // Don't throw error as this is supplementary to placement\n  }\n}\n\n// Bulk update tree counts for multiple users (for maintenance)\nexport async function bulkUpdateTreeCounts(userIds: string[]): Promise<void> {\n  try {\n    const updatePromises = userIds.map(userId => updateCachedDownlineCounts(userId));\n    await Promise.all(updatePromises);\n  } catch (error) {\n    console.error('Bulk update tree counts error:', error);\n  }\n}\n\n// Get tree health statistics\nexport async function getTreeHealthStats(rootUserId: string): Promise<{\n  totalUsers: number;\n  balanceRatio: number; // Ratio of smaller side to larger side (0-1, closer to 1 is more balanced)\n  averageDepth: number;\n  maxDepth: number;\n  emptyPositions: number; // Available spots in the tree\n}> {\n  try {\n    const teamCounts = await getCachedDownlineCounts(rootUserId);\n    const totalUsers = teamCounts.total;\n\n    // Calculate balance ratio\n    const smallerSide = Math.min(teamCounts.left, teamCounts.right);\n    const largerSide = Math.max(teamCounts.left, teamCounts.right);\n    const balanceRatio = largerSide > 0 ? smallerSide / largerSide : 1;\n\n    // Calculate tree depth statistics\n    let maxDepth = 0;\n    let totalDepth = 0;\n    let userCount = 0;\n\n    // BFS to calculate depths\n    const queue = [{ userId: rootUserId, depth: 0 }];\n    const visited = new Set<string>();\n\n    while (queue.length > 0) {\n      const { userId, depth } = queue.shift()!;\n\n      if (visited.has(userId)) continue;\n      visited.add(userId);\n\n      maxDepth = Math.max(maxDepth, depth);\n      totalDepth += depth;\n      userCount++;\n\n      const referrals = await prisma.referral.findMany({\n        where: { referrerId: userId },\n        select: { referredId: true },\n      });\n\n      for (const referral of referrals) {\n        if (!visited.has(referral.referredId)) {\n          queue.push({ userId: referral.referredId, depth: depth + 1 });\n        }\n      }\n    }\n\n    const averageDepth = userCount > 0 ? totalDepth / userCount : 0;\n\n    // Calculate empty positions (theoretical max - actual users)\n    const theoreticalMax = Math.pow(2, maxDepth + 1) - 1;\n    const emptyPositions = Math.max(0, theoreticalMax - totalUsers);\n\n    return {\n      totalUsers,\n      balanceRatio,\n      averageDepth,\n      maxDepth,\n      emptyPositions,\n    };\n  } catch (error) {\n    console.error('Tree health stats error:', error);\n    return {\n      totalUsers: 0,\n      balanceRatio: 1,\n      averageDepth: 0,\n      maxDepth: 0,\n      emptyPositions: 0,\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,eAAe,qBAAqB,MAAc;IACvD,IAAI;QACF,MAAM,oBAAoB,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YACtD,OAAO;gBACL;gBACA,QAAQ;gBACR,YAAY;oBACV,IAAI,IAAI;gBACV;YACF;QACF;QAEA,OAAO,oBAAoB;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAGO,eAAe,uBAAuB,MAAc,EAAE,IAAsB;IACjF,IAAI;QACF,MAAM,gBAAgB,MAAM,iBAAiB,QAAQ;QACrD,OAAO,cAAc,MAAM;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAEA,wDAAwD;AACxD,eAAe,6BAA6B,UAAkB;IAC5D,IAAI;QACF,iDAAiD;QACjD,MAAM,oBAAoB,MAAM,uBAAuB,YAAY;QACnE,MAAM,qBAAqB,MAAM,uBAAuB,YAAY;QAEpE,qDAAqD;QACrD,MAAM,aAA+B,qBAAqB,qBAAqB,SAAS;QAExF,iDAAiD;QACjD,MAAM,gBAAgB,MAAM,2BAA2B,YAAY;QAEnE,IAAI,eAAe;YACjB,OAAO;QACT;QAEA,+DAA+D;QAC/D,MAAM,eAAiC,eAAe,SAAS,UAAU;QACzE,MAAM,eAAe,MAAM,2BAA2B,YAAY;QAElE,IAAI,cAAc;YAChB,OAAO;QACT;QAEA,gDAAgD;QAChD,MAAM,oBAAoB,MAAM,wHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;QAC5D,MAAM,UAAU,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;QAChE,MAAM,WAAW,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;QAEjE,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,QAAQ;gBAAY,MAAM;YAAO;QAC5C,OAAO,IAAI,CAAC,UAAU;YACpB,OAAO;gBAAE,QAAQ;gBAAY,MAAM;YAAQ;QAC7C;QAEA,uDAAuD;QACvD,OAAO;YAAE,QAAQ;YAAY,MAAM;QAAW;IAEhD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,wBAAwB;QACxB,OAAO;YAAE,QAAQ;YAAY,MAAM;QAAO;IAC5C;AACF;AAGO,eAAe,sBAAsB,UAAkB,EAAE,SAAiB;IAC/E,IAAI;QACF,sEAAsE;QACtE,MAAM,kBAAkB,MAAM,6BAA6B;QAE3D,uDAAuD;QACvD,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YACtB,YAAY,gBAAgB,MAAM;YAClC,YAAY;YACZ,eAAe,gBAAgB,IAAI;QACrC;QAEA,8CAA8C;QAC9C,MAAM,aAAa,gBAAgB,IAAI,KAAK,SACxC;YAAE,gBAAgB;QAAU,IAC5B;YAAE,iBAAiB;QAAU;QAEjC,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI,gBAAgB,MAAM;YAAC;YACpC,MAAM;QACR;QAEA,+DAA+D;QAC/D,8EAA8E;QAC9E,MAAM,0BAA0B,YAAY;QAE5C,+CAA+C;QAC/C,MAAM,+BAA+B,YAAY,gBAAgB,MAAM;QAEvE,OAAO,gBAAgB,IAAI;IAE7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAEA,+DAA+D;AAC/D,eAAe,0BAA0B,SAAiB,EAAE,SAAiB;IAC3E,IAAI;QACF,0DAA0D;QAC1D,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBAAE,YAAY;YAAU;QAChC;QAEA,yCAAyC;QACzC,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ,qBAAqB;oBAAE,WAAW;gBAAE;gBACpC,WAAW,IAAI;YACjB;QACF;QAEA,wFAAwF;QACxF,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC/B,OAAO;gBACL,YAAY;gBACZ,YAAY;YACd;YACA,MAAM;gBACJ,iBAAiB;YACnB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;IACtD,iEAAiE;IACnE;AACF;AAGO,eAAe,2BAA2B,MAAc;IAC7D,IAAI;QACF,MAAM,YAAY,MAAM,uBAAuB,QAAQ;QACvD,MAAM,aAAa,MAAM,uBAAuB,QAAQ;QAExD,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBACJ,mBAAmB;gBACnB,oBAAoB;gBACpB,gBAAgB,IAAI;YACtB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;IACxD;AACF;AAGO,eAAe,wBAAwB,MAAc;IAC1D,IAAI;QACF,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAO;YACpB,QAAQ;gBACN,mBAAmB;gBACnB,oBAAoB;gBACpB,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,MAAM;gBAAG,OAAO;gBAAG,OAAO;YAAE;QACvC;QAEA,6EAA6E;QAC7E,MAAM,WAAW,KAAK,cAAc,GAAG,KAAK,GAAG,KAAK,KAAK,cAAc,CAAC,OAAO,KAAK;QACpF,MAAM,qBAAqB,KAAK,KAAK,MAAM,aAAa;QAExD,IAAI,WAAW,sBAAsB,KAAK,iBAAiB,KAAK,QAAQ,KAAK,kBAAkB,KAAK,MAAM;YACxG,oBAAoB;YACpB,OAAO;gBACL,MAAM,KAAK,iBAAiB;gBAC5B,OAAO,KAAK,kBAAkB;gBAC9B,OAAO,KAAK,iBAAiB,GAAG,KAAK,kBAAkB;YACzD;QACF,OAAO;YACL,oDAAoD;YACpD,MAAM,YAAY,MAAM,uBAAuB,QAAQ;YACvD,MAAM,aAAa,MAAM,uBAAuB,QAAQ;YAExD,8BAA8B;YAC9B,2BAA2B,QAAQ,KAAK,CAAC,QAAQ,KAAK;YAEtD,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO,YAAY;YACrB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,MAAM;YAAG,OAAO;YAAG,OAAO;QAAE;IACvC;AACF;AAEA,sGAAsG;AACtG,eAAe,2BAA2B,UAAkB,EAAE,UAA4B;IACxF,IAAI;QACF,gEAAgE;QAChE,MAAM,gBAAgB,MAAM,2BAA2B,YAAY;QAEnE,IAAI,eAAe;YACjB,OAAO;QACT;QAEA,8EAA8E;QAC9E,MAAM,YAAY,MAAM,iBAAiB,YAAY;QAErD,oEAAoE;QACpE,IAAI,cAAc;QAClB,IAAI,mBAAmB;QAEvB,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,YAAY,MAAM,uBAAuB,SAAS,EAAE,EAAE;YAC5D,MAAM,aAAa,MAAM,uBAAuB,SAAS,EAAE,EAAE;YAC7D,MAAM,gBAAgB,YAAY;YAElC,IAAI,gBAAgB,kBAAkB;gBACpC,yCAAyC;gBACzC,MAAM,gBAAgB,MAAM,wHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE;gBACnE,MAAM,UAAU,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;gBAC5D,MAAM,WAAW,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;gBAE7D,IAAI,CAAC,WAAW,CAAC,UAAU;oBACzB,mBAAmB;oBACnB,cAAc,SAAS,EAAE;gBAC3B;YACF;QACF;QAEA,wDAAwD;QACxD,MAAM,uBAAuB,MAAM,wHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;QAC/D,MAAM,UAAU,qBAAqB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;QACnE,MAAM,WAAW,qBAAqB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;QAEpE,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,QAAQ;gBAAa,MAAM;YAAO;QAC7C,OAAO,IAAI,CAAC,UAAU;YACpB,OAAO;gBAAE,QAAQ;gBAAa,MAAM;YAAQ;QAC9C;QAEA,+CAA+C;QAC/C,MAAM,YAAY,MAAM,uBAAuB,aAAa;QAC5D,MAAM,aAAa,MAAM,uBAAuB,aAAa;QAC7D,MAAM,aAA+B,aAAa,aAAa,SAAS;QAExE,OAAO;YAAE,QAAQ;YAAa,MAAM;QAAW;IAEjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,QAAQ;YAAY,MAAM;QAAW;IAChD;AACF;AAEA,sFAAsF;AACtF,eAAe,wBAAwB,UAAkB;IACvD,IAAI;QACF,+DAA+D;QAC/D,IAAI,gBAAgB;QACpB,IAAI,eAAe;QACnB,MAAM,WAAW,IAAI,yBAAyB;QAE9C,MAAO,eAAe,SAAU;YAC9B,6BAA6B;YAC7B,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI;gBAAc;gBAC3B,QAAQ;oBAAE,IAAI;gBAAK;YACrB;YAEA,IAAI,CAAC,YAAY;gBACf,2CAA2C;gBAC3C,OAAO;oBAAE,QAAQ;oBAAY,MAAM;gBAAO;YAC5C;YAEA,kDAAkD;YAClD,MAAM,mBAAmB,MAAM,wHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;YAC3D,MAAM,UAAU,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;YAE/D,IAAI,CAAC,SAAS;gBACZ,+BAA+B;gBAC/B,OAAO;oBAAE,QAAQ;oBAAe,MAAM;gBAAO;YAC/C;YAEA,iDAAiD;YACjD,MAAM,YAAY,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;YACjE,IAAI,CAAC,WAAW;gBACd,6DAA6D;gBAC7D,OAAO;oBAAE,QAAQ;oBAAe,MAAM;gBAAO;YAC/C;YAEA,gBAAgB,UAAU,UAAU;YACpC;QACF;QAEA,yDAAyD;QACzD,OAAO;YAAE,QAAQ;YAAe,MAAM;QAAO;IAE/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,QAAQ;YAAY,MAAM;QAAO;IAC5C;AACF;AAEA,wFAAwF;AACxF,eAAe,yBAAyB,UAAkB;IACxD,IAAI;QACF,gEAAgE;QAChE,IAAI,gBAAgB;QACpB,IAAI,eAAe;QACnB,MAAM,WAAW,IAAI,yBAAyB;QAE9C,MAAO,eAAe,SAAU;YAC9B,6BAA6B;YAC7B,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI;gBAAc;gBAC3B,QAAQ;oBAAE,IAAI;gBAAK;YACrB;YAEA,IAAI,CAAC,YAAY;gBACf,2CAA2C;gBAC3C,OAAO;oBAAE,QAAQ;oBAAY,MAAM;gBAAQ;YAC7C;YAEA,mDAAmD;YACnD,MAAM,mBAAmB,MAAM,wHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;YAC3D,MAAM,WAAW,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;YAEhE,IAAI,CAAC,UAAU;gBACb,gCAAgC;gBAChC,OAAO;oBAAE,QAAQ;oBAAe,MAAM;gBAAQ;YAChD;YAEA,kDAAkD;YAClD,MAAM,aAAa,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;YAClE,IAAI,CAAC,YAAY;gBACf,8DAA8D;gBAC9D,OAAO;oBAAE,QAAQ;oBAAe,MAAM;gBAAQ;YAChD;YAEA,gBAAgB,WAAW,UAAU;YACrC;QACF;QAEA,yDAAyD;QACzD,OAAO;YAAE,QAAQ;YAAe,MAAM;QAAQ;IAEhD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,QAAQ;YAAY,MAAM;QAAQ;IAC7C;AACF;AAGO,eAAe,wBAAwB,UAAkB,EAAE,SAAiB,EAAE,IAAsB;IACzG,IAAI;QACF,4DAA4D;QAC5D,MAAM,kBAAkB,MAAM,2BAA2B,YAAY;QAErE,uDAAuD;QACvD,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YACtB,YAAY,gBAAgB,MAAM;YAClC,YAAY;YACZ,eAAe,gBAAgB,IAAI;QACrC;QAEA,kCAAkC;QAClC,MAAM,aAAa,gBAAgB,IAAI,KAAK,SACxC;YAAE,gBAAgB;QAAU,IAC5B;YAAE,iBAAiB;QAAU;QAEjC,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI,gBAAgB,MAAM;YAAC;YACpC,MAAM;QACR;QAEA,+DAA+D;QAC/D,MAAM,0BAA0B,YAAY;QAE5C,+CAA+C;QAC/C,MAAM,+BAA+B,YAAY,gBAAgB,MAAM;QAEvE,OAAO,gBAAgB,IAAI;IAE7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,eAAe,wBAAwB,UAAkB,EAAE,SAAiB;IACjF,IAAI;QACF,uDAAuD;QACvD,MAAM,kBAAkB,MAAM,wBAAwB;QAEtD,uDAAuD;QACvD,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YACtB,YAAY,gBAAgB,MAAM;YAClC,YAAY;YACZ,eAAe,gBAAgB,IAAI;QACrC;QAEA,uCAAuC;QACvC,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI,gBAAgB,MAAM;YAAC;YACpC,MAAM;gBAAE,gBAAgB;YAAU;QACpC;QAEA,+DAA+D;QAC/D,MAAM,0BAA0B,YAAY;QAE5C,+CAA+C;QAC/C,MAAM,+BAA+B,YAAY,gBAAgB,MAAM;QAEvE,OAAO,gBAAgB,IAAI;IAE7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,eAAe,yBAAyB,UAAkB,EAAE,SAAiB;IAClF,IAAI;QACF,wDAAwD;QACxD,MAAM,kBAAkB,MAAM,yBAAyB;QAEvD,uDAAuD;QACvD,MAAM,wHAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YACtB,YAAY,gBAAgB,MAAM;YAClC,YAAY;YACZ,eAAe,gBAAgB,IAAI;QACrC;QAEA,wCAAwC;QACxC,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI,gBAAgB,MAAM;YAAC;YACpC,MAAM;gBAAE,iBAAiB;YAAU;QACrC;QAEA,+DAA+D;QAC/D,MAAM,0BAA0B,YAAY;QAE5C,+CAA+C;QAC/C,MAAM,+BAA+B,YAAY,gBAAgB,MAAM;QAEvE,OAAO,gBAAgB,IAAI;IAE7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAGO,eAAe,wBACpB,UAAkB,EAClB,SAAiB,EACjB,YAA0C;IAE1C,IAAI;QACF,OAAQ;YACN,KAAK;gBACH,mEAAmE;gBACnE,OAAO,MAAM,wBAAwB,YAAY;YAEnD,KAAK;gBACH,qEAAqE;gBACrE,OAAO,MAAM,yBAAyB,YAAY;YAEpD,KAAK;YACL;gBACE,+BAA+B;gBAC/B,OAAO,MAAM,sBAAsB,YAAY;QACnD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,6CAA6C;AAC7C,eAAe,2BAA2B,UAAkB,EAAE,UAA4B;IACxF,IAAI;QACF,uCAAuC;QACvC,MAAM,gBAAgB,MAAM,wHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;QACxD,MAAM,aAAa,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;QAE/D,IAAI,CAAC,YAAY;YACf,sCAAsC;YACtC,OAAO;gBAAE,QAAQ;gBAAY,MAAM;YAAW;QAChD;QAEA,yDAAyD;QACzD,MAAM,QAAQ;YAAC,WAAW,UAAU;SAAC;QAErC,MAAO,MAAM,MAAM,GAAG,EAAG;YACvB,MAAM,gBAAgB,MAAM,KAAK;YACjC,MAAM,mBAAmB,MAAM,wHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;YAE3D,yCAAyC;YACzC,MAAM,UAAU,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;YAC/D,MAAM,WAAW,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,aAAa,KAAK;YAEhE,IAAI,CAAC,SAAS;gBACZ,OAAO;oBAAE,QAAQ;oBAAe,MAAM;gBAAO;YAC/C;YACA,IAAI,CAAC,UAAU;gBACb,OAAO;oBAAE,QAAQ;oBAAe,MAAM;gBAAQ;YAChD;YAEA,8CAA8C;YAC9C,iBAAiB,OAAO,CAAC,CAAA;gBACvB,MAAM,IAAI,CAAC,EAAE,UAAU;YACzB;QACF;QAEA,OAAO,MAAM,0BAA0B;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAKO,eAAe,2BAA2B,UAAkB,EAAE,gBAAwB,EAAE,WAAoB;IACjH,IAAI;QACF,uDAAuD;QACvD,MAAM,WAAW,MAAM,qBAAqB;QAE5C,IAAI,CAAC,UAAU;YACb,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,WAAW,yBAAyB,CAAC;YACxG,OAAO,GAAG,4CAA4C;QACxD;QAEA,+EAA+E;QAC/E,IAAI,aAAa;YACf,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC7C,OAAO;oBAAE,IAAI;gBAAY;gBACzB,QAAQ;oBAAE,4BAA4B;oBAAM,WAAW;oBAAM,UAAU;gBAAK;YAC9E;YAEA,IAAI,WAAW,4BAA4B;gBACzC,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,WAAW,6CAA6C,EAAE,YAAY,EAAE,EAAE,UAAU,SAAS,CAAC,CAAC,EAAE,UAAU,QAAQ,CAAC,CAAC,CAAC;gBAC9K,OAAO,GAAG,+CAA+C;YAC3D;QACF;QAEA,MAAM,kBAAkB,WAAW,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,4BAA4B;QACzF,MAAM,cAAc,AAAC,mBAAmB,kBAAmB;QAE3D,sDAAsD;QACtD,MAAM,gBAAgB,MAAM,wHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;QACxD,MAAM,wHAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,YAAY;YAC9C,kBAAkB,cAAc,gBAAgB,GAAG;QACrD;QAEA,qEAAqE;QACrE,MAAM,wHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;YACzB,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,aAAa,CAAC,uBAAuB,EAAE,gBAAgB,MAAM,EAAE,iBAAiB,kBAAkB,CAAC;YACnG,WAAW,cAAc,CAAC,UAAU,EAAE,aAAa,GAAG;YACtD,QAAQ;QACV;QAEA,oCAAoC;QACpC,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC/B,OAAO;gBACL;gBACA,UAAU;oBACR,aAAa;wBACX,MAAM;4BACJ;wBACF;oBACF;gBACF;YACF;YACA,MAAM;gBACJ,kBAAkB;oBAChB,WAAW;gBACb;YACF;QACF;QAEA,+DAA+D;QAC/D,IAAI,aAAa;YACf,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACvB,OAAO;oBAAE,IAAI;gBAAY;gBACzB,MAAM;oBAAE,4BAA4B;gBAAK;YAC3C;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,YAAY,2BAA2B,EAAE,WAAW,WAAW,EAAE,aAAa;QAClI,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,eAAe,gBAAgB,MAAc,EAAE,gBAAwB;IAC5E,IAAI;QACF,yEAAyE;QACzE,6CAA6C;QAC7C,MAAM,SAAS,KAAK,KAAK,CAAC,AAAC,mBAAmB,MAAO,OAAO,KAAK,4BAA4B;QAE7F,IAAI,UAAU,GAAG,QAAQ,mDAAmD;QAE5E,qFAAqF;QACrF,MAAM,cAAc,MAAM,eAAe;QAEzC,KAAK,MAAM,cAAc,YAAa;YACpC,2DAA2D;YAC3D,MAAM,WAAW,MAAM,qBAAqB,WAAW,EAAE;YAEzD,IAAI,CAAC,UAAU;gBACb,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,EAAE,CAAC,yBAAyB,CAAC;gBAC9E,UAAU,sBAAsB;YAClC;YAEA,0DAA0D;YAC1D,MAAM,gBAAgB,MAAM,qBAAqB,WAAW,EAAE,EAAE;YAEhE,IAAI,eAAe;gBACjB,0CAA0C;gBAC1C,MAAM,sBAAsB,MAAM,wHAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,WAAW,EAAE;gBAE3E,kCAAkC;gBAClC,MAAM,mBAAmB,WAAW,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,iCAAiC;gBAE/F,0CAA0C;gBAC1C,MAAM,oBAAoB,qBAAqB,cAAc;gBAC7D,MAAM,qBAAqB,qBAAqB,eAAe;gBAE/D,IAAI,cAAc;gBAClB,IAAI,eAAiC;gBAErC,IAAI,kBAAkB,QAAQ;oBAC5B,6CAA6C;oBAC7C,IAAI,qBAAqB,kBAAkB;wBACzC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,gCAAgC,EAAE,kBAAkB,CAAC,EAAE,iBAAiB,mBAAmB,CAAC;wBAC9H,UAAU,kCAAkC;oBAC9C;oBAEA,qEAAqE;oBACrE,cAAc,KAAK,GAAG,CAAC,QAAQ,mBAAmB;gBACpD,OAAO;oBACL,8CAA8C;oBAC9C,IAAI,sBAAsB,kBAAkB;wBAC1C,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,iCAAiC,EAAE,mBAAmB,CAAC,EAAE,iBAAiB,mBAAmB,CAAC;wBAChI,UAAU,kCAAkC;oBAC9C;oBAEA,qEAAqE;oBACrE,cAAc,KAAK,GAAG,CAAC,QAAQ,mBAAmB;gBACpD;gBAEA,kCAAkC;gBAClC,IAAI,cAAc,GAAG;oBACnB,MAAM,aAAa,kBAAkB,SACjC;wBAAE,YAAY;oBAAY,IAC1B;wBAAE,aAAa;oBAAY;oBAE/B,MAAM,wHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;wBAC1B,QAAQ,WAAW,EAAE;wBACrB,GAAG,UAAU;oBACf;oBAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,YAAY,WAAW,EAAE,cAAc,sBAAsB,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,cAAc,SAAS,oBAAoB,cAAc,CAAC,CAAC;gBACnK;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEA,wCAAwC;AACxC,eAAe,eAAe,MAAc;IAC1C,IAAI;QACF,MAAM,cAAc,EAAE;QACtB,IAAI,gBAAgB;QAEpB,qEAAqE;QACrE,IAAK,IAAI,QAAQ,GAAG,QAAQ,IAAI,QAAS;YACvC,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC/C,OAAO;oBAAE,YAAY;gBAAc;gBACnC,SAAS;oBACP,UAAU;wBACR,QAAQ;4BAAE,IAAI;4BAAM,OAAO;wBAAK;oBAClC;gBACF;YACF;YAEA,IAAI,CAAC,UAAU;YAEf,YAAY,IAAI,CAAC,SAAS,QAAQ;YAClC,gBAAgB,SAAS,UAAU;QACrC;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF;AAEA,qEAAqE;AACrE,eAAe,qBAAqB,MAAc;IAChD,IAAI;QACF,MAAM,cAAc,EAAE;QACtB,IAAI,gBAAgB;QAEpB,qEAAqE;QACrE,IAAK,IAAI,QAAQ,GAAG,QAAQ,IAAI,QAAS;YACvC,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC/C,OAAO;oBAAE,YAAY;gBAAc;gBACnC,SAAS;oBACP,UAAU;wBACR,QAAQ;4BAAE,IAAI;4BAAM,OAAO;4BAAM,UAAU;wBAAK;oBAClD;gBACF;YACF;YAEA,IAAI,CAAC,UAAU;YAEf,oCAAoC;YACpC,IAAI,SAAS,QAAQ,CAAC,QAAQ,EAAE;gBAC9B,YAAY,IAAI,CAAC,SAAS,QAAQ;YACpC;YAEA,qDAAqD;YACrD,gBAAgB,SAAS,UAAU;QACrC;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,EAAE;IACX;AACF;AAEA,+DAA+D;AAC/D,eAAe,qBAAqB,YAAoB,EAAE,MAAc;IACtE,IAAI;QACF,+BAA+B;QAC/B,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,YAAY;gBACZ,YAAY;YACd;QACF;QAEA,IAAI,gBAAgB;YAClB,OAAO,eAAe,aAAa;QACrC;QAEA,uDAAuD;QACvD,MAAM,gBAAgB,MAAM,iBAAiB,cAAc;QAC3D,MAAM,iBAAiB,MAAM,iBAAiB,cAAc;QAE5D,IAAI,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;YAC5C,OAAO;QACT;QAEA,IAAI,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;YAC7C,OAAO;QACT;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAEA,6CAA6C;AAC7C,eAAe,iBAAiB,MAAc,EAAE,IAAsB;IACpE,IAAI;QACF,MAAM,gBAAgB,EAAE;QACxB,MAAM,UAAU,IAAI;QAEpB,wDAAwD;QACxD,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACtD,OAAO;gBACL,YAAY;gBACZ,eAAe;YACjB;YACA,QAAQ;gBACN,YAAY;YACd;QACF;QAEA,yCAAyC;QACzC,MAAM,QAAQ,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;QAEpD,MAAO,MAAM,MAAM,GAAG,EAAG;YACvB,MAAM,gBAAgB,MAAM,KAAK;YAEjC,mDAAmD;YACnD,IAAI,QAAQ,GAAG,CAAC,gBAAgB;YAChC,QAAQ,GAAG,CAAC;YAEZ,+BAA+B;YAC/B,cAAc,IAAI,CAAC;gBAAE,IAAI;YAAc;YAEvC,4DAA4D;YAC5D,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC/C,OAAO;oBACL,YAAY;gBACd;gBACA,QAAQ;oBACN,YAAY;gBACd;YACF;YAEA,kDAAkD;YAClD,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,UAAU,GAAG;oBACrC,MAAM,IAAI,CAAC,SAAS,UAAU;gBAChC;YACF;QACF;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,EAAE;IACX;AACF;AAEA,oEAAoE;AACpE,eAAe,oBAAoB,MAAc;IAC/C,IAAI;QACF,MAAM,gBAAgB,EAAE;QACxB,MAAM,UAAU,IAAI;QAEpB,iDAAiD;QACjD,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACtD,OAAO;gBACL,YAAY;YACd;YACA,QAAQ;gBACN,YAAY;YACd;QACF;QAEA,6CAA6C;QAC7C,MAAM,QAAQ,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;QAEpD,MAAO,MAAM,MAAM,GAAG,EAAG;YACvB,MAAM,gBAAgB,MAAM,KAAK;YAEjC,mDAAmD;YACnD,IAAI,QAAQ,GAAG,CAAC,gBAAgB;YAChC,QAAQ,GAAG,CAAC;YAEZ,wCAAwC;YACxC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,OAAO;oBAAE,IAAI;gBAAc;gBAC3B,QAAQ;oBAAE,IAAI;oBAAM,UAAU;gBAAK;YACrC;YAEA,IAAI,MAAM;gBACR,cAAc,IAAI,CAAC;oBAAE,IAAI,KAAK,EAAE;oBAAE,UAAU,KAAK,QAAQ;gBAAC;gBAE1D,sCAAsC;gBACtC,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAC/C,OAAO;wBACL,YAAY;oBACd;oBACA,QAAQ;wBACN,YAAY;oBACd;gBACF;gBAEA,kDAAkD;gBAClD,KAAK,MAAM,YAAY,UAAW;oBAChC,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,UAAU,GAAG;wBACrC,MAAM,IAAI,CAAC,SAAS,UAAU;oBAChC;gBACF;YACF;QACF;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,EAAE;IACX;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,mBAAmB,WAAW,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,iCAAiC;QAC/F,MAAM,aAAa,WAAW,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,yBAAyB,OAAO,oCAAoC;QAE5H,mCAAmC;QACnC,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YACzD,OAAO;gBACL,IAAI;oBACF;wBAAE,YAAY;4BAAE,IAAI;wBAAE;oBAAE;oBACxB;wBAAE,aAAa;4BAAE,IAAI;wBAAE;oBAAE;iBAC1B;YACH;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBAAE,IAAI;wBAAM,OAAO;oBAAK;gBAClC;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,gBAAgB,MAAM,CAAC,MAAM,CAAC;QAE5E,MAAM,kBAAkB,EAAE;QAE1B,KAAK,MAAM,cAAc,gBAAiB;YACxC,IAAI;gBACF,gFAAgF;gBAChF,MAAM,aAAa,KAAK,GAAG,CAAC,WAAW,UAAU,EAAE;gBACnD,MAAM,cAAc,KAAK,GAAG,CAAC,WAAW,WAAW,EAAE;gBACrD,MAAM,gBAAgB,KAAK,GAAG,CAAC,YAAY;gBAE3C,IAAI,gBAAgB,GAAG;oBACrB,yCAAyC;oBACzC,MAAM,aAAa,gBAAgB;oBAEnC,IAAI;wBACF,kCAAkC;wBAClC,MAAM,wHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;4BACzB,QAAQ,WAAW,MAAM;4BACzB,MAAM;4BACN,QAAQ;4BACR,aAAa,CAAC,wBAAwB,EAAE,cAAc,oBAAoB,EAAE,WAAW,UAAU,CAAC;4BAClG,QAAQ;wBACV;wBAEA,wBAAwB;wBACxB,MAAM,wHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,WAAW,MAAM,EAAE;wBAErD,qEAAqE;wBACrE,kFAAkF;wBAClF,MAAM,sBAAsB,KAAK,GAAG,CAAC,GAAG,WAAW,UAAU,GAAG;wBAChE,MAAM,uBAAuB,KAAK,GAAG,CAAC,GAAG,WAAW,WAAW,GAAG;wBAElE,0EAA0E;wBAC1E,MAAM,kBAAkB,WAAW,UAAU,GAAG,WAAW,WAAW,GAAG,sBAAsB;wBAC/F,MAAM,mBAAmB,WAAW,WAAW,GAAG,WAAW,UAAU,GAAG,uBAAuB;wBAEjG,8EAA8E;wBAC9E,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;4BAC/B,OAAO;gCAAE,IAAI,WAAW,EAAE;4BAAC;4BAC3B,MAAM;gCACJ,YAAY;gCACZ,aAAa;gCACb,eAAe;oCAAE,WAAW;gCAAc;gCAC1C,cAAc;oCAAE,WAAW;gCAAc;gCACzC,eAAe,IAAI;gCACnB,WAAW,IAAI;4BACjB;wBACF;wBAEA,gBAAgB,IAAI,CAAC;4BACnB,QAAQ,WAAW,MAAM;4BACzB;4BACA,QAAQ;4BACR,qBAAqB;4BACrB,sBAAsB;wBACxB;wBAEA,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,CAAC,EAAE,EAAE,cAAc,kBAAkB,EAAE,WAAW,OAAO,CAAC,GAAG,qBAAqB,EAAE,gBAAgB,EAAE,EAAE,kBAAkB;oBACjK,EAAE,OAAO,aAAa;wBACpB,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,WAAW,MAAM,CAAC,CAAC,CAAC,EAAE;oBACxE,gEAAgE;oBAClE;gBACF,OAAO;oBACL,wEAAwE;oBACxE,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,WAAW,UAAU,GAAG;oBACvD,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,WAAW,WAAW,GAAG;oBAEzD,IAAI,aAAa,KAAK,cAAc,GAAG;wBACrC,IAAI;4BACF,qCAAqC;4BACrC,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gCAC/B,OAAO;oCAAE,IAAI,WAAW,EAAE;gCAAC;gCAC3B,MAAM;oCACJ,YAAY,KAAK,GAAG,CAAC,WAAW,UAAU,EAAE;oCAC5C,aAAa,KAAK,GAAG,CAAC,WAAW,WAAW,EAAE;oCAC9C,WAAW,IAAI;gCACjB;4BACF;4BAEA,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,WAAW,MAAM,CAAC,yBAAyB,EAAE,WAAW,EAAE,EAAE,YAAY,eAAe,CAAC;wBAC9G,EAAE,OAAO,YAAY;4BACnB,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,WAAW,MAAM,CAAC,CAAC,CAAC,EAAE;wBAC/E;oBACF;gBACF;YAEF,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,WAAW,MAAM,CAAC,CAAC,CAAC,EAAE;YACnF;QACF;QAEA,kCAAkC;QAClC,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,SAAS;gBACP,gBAAgB,gBAAgB,MAAM;gBACtC,oBAAoB,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;gBAC9E;gBACA,cAAc,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;gBACjE,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,gBAAgB,MAAM,CAAC,4BAA4B,EAAE,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI;QAE3K,OAAO;YACL,SAAS;YACT,gBAAgB,gBAAgB,MAAM;YACtC,cAAc,gBAAgB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YACjE;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,eAAe,eAAe,MAAc;IACjD,IAAI;QACF,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAO;YACpB,QAAQ;gBAAE,YAAY;YAAK;QAC7B;QAEA,IAAI,CAAC,MAAM,YAAY,OAAO;QAE9B,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC3C,OAAO;gBAAE,IAAI,KAAK,UAAU;YAAC;YAC7B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,WAAW;gBACX,UAAU;YACZ;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAGO,eAAe,uBAAuB,MAAc;IACzD,IAAI;QACF,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACpC,OAAO;gBAAE,YAAY;YAAO;QAC9B;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,eAAe,kBAAkB,MAAc;IACpD,IAAI;QACF,OAAO,MAAM,wBAAwB;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YAAE,MAAM;YAAG,OAAO;YAAG,OAAO;QAAE;IACvC;AACF;AAGO,eAAe,qBAAqB,MAAc;IAQvD,IAAI;QACF,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAO;YACpB,QAAQ;gBAAE,qBAAqB;YAAK;QACtC;QAEA,MAAM,aAAa,MAAM,wBAAwB;QAEjD,0DAA0D;QAC1D,MAAM,mBAAmB,MAAM,oBAAoB;QACnD,MAAM,gBAAgB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QAErE,0DAA0D;QAC1D,MAAM,gBAAgB,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;QAChE,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1C,OAAO;gBACL,YAAY;gBACZ,WAAW;oBAAE,KAAK;gBAAc;YAClC;QACF;QAEA,OAAO;YACL,iBAAiB,MAAM,uBAAuB;YAC9C,UAAU,WAAW,IAAI;YACzB,WAAW,WAAW,KAAK;YAC3B,WAAW,WAAW,KAAK;YAC3B;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,iBAAiB;YACjB,UAAU;YACV,WAAW;YACX,WAAW;YACX,eAAe;YACf,aAAa;QACf;IACF;AACF;AAGO,eAAe,qBAAqB,MAAc,EAAE,UAAkB;IAQ3E,IAAI;QACF,IAAI,cAAc,GAAG,OAAO,EAAE;QAE9B,IAAI,oBAAoB;YAAC;gBAAE,IAAI;gBAAQ,MAAM;YAAgC;SAAE;QAE/E,IAAK,IAAI,QAAQ,GAAG,SAAS,YAAY,QAAS;YAChD,MAAM,iBAAiB,EAAE;YAEzB,KAAK,MAAM,eAAe,kBAAmB;gBAC3C,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAC/C,OAAO;wBAAE,YAAY,YAAY,EAAE;oBAAC;oBACpC,SAAS;wBACP,UAAU;4BACR,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;gCACV,WAAW;4BACb;wBACF;oBACF;gBACF;gBAEA,KAAK,MAAM,YAAY,UAAW;oBAChC,eAAe,IAAI,CAAC;wBAClB,IAAI,SAAS,UAAU;wBACvB,MAAM,SAAS,aAAa;oBAC9B;gBACF;YACF;YAEA,oBAAoB;QACtB;QAEA,iDAAiD;QACjD,MAAM,cAAc,MAAM,QAAQ,GAAG,CACnC,kBAAkB,GAAG,CAAC,OAAO;YAC3B,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC5C,OAAO;oBAAE,IAAI,KAAK,EAAE;gBAAC;gBACrB,QAAQ;oBACN,IAAI;oBACJ,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,WAAW;gBACb;YACF;YAEA,OAAO;gBACL,GAAG,QAAQ;gBACX,eAAe,KAAK,IAAI;YAC1B;QACF;QAGF,OAAO,YAAY,MAAM,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAGO,eAAe,uBAAuB,MAAc,EAAE,QAAQ,CAAC,EAAE,gBAA6B,IAAI,KAAK;IAC5G,IAAI;QACF,MAAM,YAAY,OAAO,eAAuB,cAAsB,OAAe,EAAE;YACrF,IAAI,gBAAgB,GAAG,OAAO;YAE9B,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,OAAO;oBAAE,IAAI;gBAAc;gBAC3B,QAAQ;oBACN,IAAI;oBACJ,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,WAAW;gBACb;YACF;YAEA,IAAI,CAAC,MAAM,OAAO;YAElB,kEAAkE;YAClE,MAAM,WAAW,MAAM,qBAAqB;YAE5C,0BAA0B;YAC1B,MAAM,cAAc,MAAM,eAAe;YAEzC,4BAA4B;YAC5B,MAAM,sBAAsB,MAAM,uBAAuB;YAEzD,kBAAkB;YAClB,MAAM,aAAa,MAAM,kBAAkB;YAE3C,0CAA0C;YAC1C,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACnD,OAAO;oBACL,YAAY;oBACZ,eAAe;gBACjB;gBACA,SAAS;oBACP,UAAU;wBACR,QAAQ;4BAAE,IAAI;4BAAM,OAAO;4BAAM,WAAW;4BAAM,UAAU;4BAAM,WAAW;wBAAK;oBACpF;gBACF;YACF;YAEA,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACpD,OAAO;oBACL,YAAY;oBACZ,eAAe;gBACjB;gBACA,SAAS;oBACP,UAAU;wBACR,QAAQ;4BAAE,IAAI;4BAAM,OAAO;4BAAM,WAAW;4BAAM,UAAU;4BAAM,WAAW;wBAAK;oBACpF;gBACF;YACF;YAEA,oBAAoB;YACpB,MAAM,eAAe,MAAM,wHAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YAEvD,kEAAkE;YAClE,uDAAuD;YACvD,kFAAkF;YAClF,2EAA2E;YAC3E,MAAM,uBAAuB,KAAK,MAAM,GAAG,GAAG,iEAAiE;YAC/G,MAAM,iBAAiB,cAAc,GAAG,CAAC;YAEzC,MAAM,qBAAqB,eAAe,KAAK,CAAC,wBAAwB,cAAc;YAEtF,sDAAsD;YACtD,MAAM,eAAe,iBAAiB;YACtC,MAAM,gBAAgB,kBAAkB;YAExC,OAAO;gBACL,MAAM;oBAAE,GAAG,IAAI;oBAAE;gBAAS;gBAC1B;gBACA;gBACA;gBACA,cAAc,gBAAgB;oBAAE,YAAY;oBAAG,aAAa;oBAAG,eAAe;gBAAE;gBAChF;gBACA;gBACA,WAAW,sBAAsB,eAC/B,MAAM,UAAU,aAAa,UAAU,EAAE,eAAe,GAAG,OAAO,OAAO;gBAC3E,YAAY,sBAAsB,gBAChC,MAAM,UAAU,cAAc,UAAU,EAAE,eAAe,GAAG,OAAO,OAAO;YAC9E;QACF;QAEA,OAAO,MAAM,UAAU,QAAQ;IAEjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,eAAe,iBAAiB,MAAc;IAInD,IAAI;QACF,0CAA0C;QAC1C,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACnD,OAAO;gBACL,YAAY;gBACZ,eAAe;YACjB;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBAAE,IAAI;wBAAM,OAAO;wBAAM,WAAW;wBAAM,UAAU;wBAAM,WAAW;oBAAK;gBACpF;YACF;QACF;QAEA,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACpD,OAAO;gBACL,YAAY;gBACZ,eAAe;YACjB;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBAAE,IAAI;wBAAM,OAAO;wBAAM,WAAW;wBAAM,UAAU;wBAAM,WAAW;oBAAK;gBACpF;YACF;QACF;QAEA,MAAM,iBAAiB,OAAO;YAC5B,IAAI,CAAC,UAAU,OAAO;YAEtB,MAAM,cAAc,SAAS,UAAU;YAEvC,kEAAkE;YAClE,MAAM,WAAW,MAAM,qBAAqB;YAE5C,0BAA0B;YAC1B,MAAM,cAAc,MAAM,eAAe;YAEzC,4BAA4B;YAC5B,MAAM,sBAAsB,MAAM,uBAAuB;YAEzD,kBAAkB;YAClB,MAAM,aAAa,MAAM,kBAAkB;YAE3C,oBAAoB;YACpB,MAAM,eAAe,MAAM,wHAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YAEvD,2CAA2C;YAC3C,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACnD,OAAO;oBAAE,YAAY;oBAAa,eAAe;gBAAO;gBACxD,QAAQ;oBAAE,IAAI;gBAAK;YACrB,OAAO;YAEP,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACpD,OAAO;oBAAE,YAAY;oBAAa,eAAe;gBAAQ;gBACzD,QAAQ;oBAAE,IAAI;gBAAK;YACrB,OAAO;YAEP,OAAO;gBACL,MAAM;oBAAE,GAAG,SAAS,QAAQ;oBAAE;gBAAS;gBACvC;gBACA;gBACA;gBACA,cAAc,gBAAgB;oBAAE,YAAY;oBAAG,aAAa;oBAAG,eAAe;gBAAE;gBAChF;gBACA;gBACA,WAAW;gBACX,YAAY;YACd;QACF;QAEA,MAAM,YAAY,MAAM,eAAe;QACvC,MAAM,aAAa,MAAM,eAAe;QAExC,OAAO;YAAE;YAAW;QAAW;IAEjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,WAAW;YAAM,YAAY;QAAK;IAC7C;AACF;AAGO,eAAe,kBAAkB,UAAkB,EAAE,UAAkB,EAAE,aAAa,EAAE;IAe7F,IAAI;QACF,MAAM,gBAAgB,CAAC,CAAC,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;QAErD,oDAAoD;QACpD,MAAM,YAAY,MAAM,iBAAiB,YAAY;QACrD,MAAM,aAAa,MAAM,iBAAiB,YAAY;QACtD,MAAM,iBAAiB;eAAI;eAAc;SAAW,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAElE,IAAI,eAAe,MAAM,KAAK,GAAG,OAAO,EAAE;QAE1C,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC/C,OAAO;gBACL,IAAI;oBAAE,IAAI;gBAAe;gBACzB,IAAI;oBACF;wBAAE,OAAO;4BAAE,UAAU;4BAAY,MAAM;wBAAc;oBAAE;oBACvD;wBAAE,WAAW;4BAAE,UAAU;4BAAY,MAAM;wBAAc;oBAAE;oBAC3D;wBAAE,UAAU;4BAAE,UAAU;4BAAY,MAAM;wBAAc;oBAAE;iBAC3D;YACH;YACA,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,YAAY;YACd;YACA,MAAM;QACR;QAEA,oDAAoD;QACpD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,cAAc,GAAG,CAAC,OAAO;YACvB,MAAM,gBAAgB,MAAM,iBAAiB,YAAY,KAAK,EAAE;YAChE,MAAM,aAAa,cAAc,KAAK,CAAC,KAAK,MAAM;YAElD,IAAI,cAAc;YAClB,IAAI,KAAK,UAAU,EAAE;gBACnB,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACzC,OAAO;wBAAE,IAAI,KAAK,UAAU;oBAAC;oBAC7B,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;YAEA,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,WAAW,KAAK,SAAS;gBACzB;gBACA;gBACA,aAAa,eAAe;YAC9B;QACF;QAGF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,EAAE;IACX;AACF;AAEA,kEAAkE;AAClE,eAAe,iBAAiB,UAAkB,EAAE,YAAoB;IACtE,IAAI;QACF,IAAI,eAAe,cAAc,OAAO;QAExC,MAAM,OAAiB,EAAE;QACzB,IAAI,gBAAgB;QAEpB,oCAAoC;QACpC,MAAO,kBAAkB,WAAY;YACnC,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC/C,OAAO;oBAAE,YAAY;gBAAc;YACrC;YAEA,IAAI,CAAC,UAAU;YAEf,KAAK,OAAO,CAAC,SAAS,aAAa,KAAK,SAAS,MAAM;YACvD,gBAAgB,SAAS,UAAU;YAEnC,yBAAyB;YACzB,IAAI,KAAK,MAAM,GAAG,IAAI;QACxB;QAEA,OAAO,KAAK,IAAI,CAAC,QAAQ;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEA,gDAAgD;AAChD,eAAe,+BAA+B,SAAiB,EAAE,iBAAyB;IACxF,IAAI;QACF,qEAAqE;QACrE,IAAI,cAAc,mBAAmB;YACnC,MAAM,2BAA2B;QACnC;QAEA,yCAAyC;QACzC,MAAM,2BAA2B;QAEjC,+DAA+D;QAC/D,MAAM,cAAc,MAAM,eAAe;QACzC,MAAM,iBAAiB,YAAY,GAAG,CAAC,CAAA,OAAQ,2BAA2B,KAAK,EAAE;QACjF,MAAM,QAAQ,GAAG,CAAC;IAEpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC3D,0DAA0D;IAC5D;AACF;AAGO,eAAe,qBAAqB,OAAiB;IAC1D,IAAI;QACF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,SAAU,2BAA2B;QACxE,MAAM,QAAQ,GAAG,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;AACF;AAGO,eAAe,mBAAmB,UAAkB;IAOzD,IAAI;QACF,MAAM,aAAa,MAAM,wBAAwB;QACjD,MAAM,aAAa,WAAW,KAAK;QAEnC,0BAA0B;QAC1B,MAAM,cAAc,KAAK,GAAG,CAAC,WAAW,IAAI,EAAE,WAAW,KAAK;QAC9D,MAAM,aAAa,KAAK,GAAG,CAAC,WAAW,IAAI,EAAE,WAAW,KAAK;QAC7D,MAAM,eAAe,aAAa,IAAI,cAAc,aAAa;QAEjE,kCAAkC;QAClC,IAAI,WAAW;QACf,IAAI,aAAa;QACjB,IAAI,YAAY;QAEhB,0BAA0B;QAC1B,MAAM,QAAQ;YAAC;gBAAE,QAAQ;gBAAY,OAAO;YAAE;SAAE;QAChD,MAAM,UAAU,IAAI;QAEpB,MAAO,MAAM,MAAM,GAAG,EAAG;YACvB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,KAAK;YAErC,IAAI,QAAQ,GAAG,CAAC,SAAS;YACzB,QAAQ,GAAG,CAAC;YAEZ,WAAW,KAAK,GAAG,CAAC,UAAU;YAC9B,cAAc;YACd;YAEA,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC/C,OAAO;oBAAE,YAAY;gBAAO;gBAC5B,QAAQ;oBAAE,YAAY;gBAAK;YAC7B;YAEA,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,UAAU,GAAG;oBACrC,MAAM,IAAI,CAAC;wBAAE,QAAQ,SAAS,UAAU;wBAAE,OAAO,QAAQ;oBAAE;gBAC7D;YACF;QACF;QAEA,MAAM,eAAe,YAAY,IAAI,aAAa,YAAY;QAE9D,6DAA6D;QAC7D,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,WAAW,KAAK;QACnD,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,iBAAiB;QAEpD,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YACL,YAAY;YACZ,cAAc;YACd,cAAc;YACd,UAAU;YACV,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 2724, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/api/mining-units/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { authenticateRequest } from '@/lib/auth';\nimport { miningUnitDb, transactionDb, adminSettingsDb, systemLogDb, walletBalanceDb } from '@/lib/database';\n\nimport { processDirectReferralBonus, addBinaryPoints, getSponsorInfo } from '@/lib/referral';\n\n// GET - Fetch user's mining units\nexport async function GET(request: NextRequest) {\n  try {\n    const { authenticated, user } = await authenticateRequest(request);\n\n    if (!authenticated || !user) {\n      return NextResponse.json(\n        { success: false, error: 'Not authenticated' },\n        { status: 401 }\n      );\n    }\n\n    const miningUnits = await miningUnitDb.findActiveByUserId(user.id);\n\n    return NextResponse.json({\n      success: true,\n      data: miningUnits,\n    });\n\n  } catch (error: any) {\n    console.error('Mining units fetch error:', error);\n    \n    return NextResponse.json(\n      { success: false, error: 'Failed to fetch mining units' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST - Purchase new mining unit\nexport async function POST(request: NextRequest) {\n  try {\n    const { authenticated, user } = await authenticateRequest(request);\n\n    if (!authenticated || !user) {\n      return NextResponse.json(\n        { success: false, error: 'Not authenticated' },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const { thsAmount, investmentAmount } = body;\n\n    // Validation\n    if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid TH/s amount or investment amount' },\n        { status: 400 }\n      );\n    }\n\n    // Get minimum purchase amount from admin settings\n    const minPurchase = parseFloat(await adminSettingsDb.get('MINIMUM_PURCHASE') || '50');\n    if (investmentAmount < minPurchase) {\n      return NextResponse.json(\n        { success: false, error: `Minimum purchase amount is $${minPurchase}` },\n        { status: 400 }\n      );\n    }\n\n    // Get TH/s price from admin settings\n    const thsPrice = parseFloat(await adminSettingsDb.get('THS_PRICE') || '50');\n    const expectedAmount = thsAmount * thsPrice;\n    \n    // Allow small rounding differences (within 1%)\n    if (Math.abs(investmentAmount - expectedAmount) > expectedAmount * 0.01) {\n      return NextResponse.json(\n        { success: false, error: 'Investment amount does not match TH/s price' },\n        { status: 400 }\n      );\n    }\n\n    // Calculate dynamic ROI based on unit size\n    const { calculateDynamicROI } = await import('@/lib/mining');\n    const dynamicROI = await calculateDynamicROI(thsAmount);\n    \n    // Check wallet balance before purchase\n    const walletBalance = await walletBalanceDb.getOrCreate(user.id);\n\n    if (walletBalance.availableBalance < investmentAmount) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: `Insufficient balance. Available: $${walletBalance.availableBalance.toFixed(2)}, Required: $${investmentAmount.toFixed(2)}`\n        },\n        { status: 400 }\n      );\n    }\n\n    // Use the calculated dynamic ROI\n    const dailyROI = dynamicROI;\n\n    // Create mining unit\n    const miningUnit = await miningUnitDb.create({\n      userId: user.id,\n      thsAmount,\n      investmentAmount,\n      dailyROI,\n    });\n\n    // Deduct amount from wallet balance\n    await walletBalanceDb.updateBalance(user.id, {\n      availableBalance: walletBalance.availableBalance - investmentAmount,\n    });\n\n    // Create purchase transaction\n    await transactionDb.create({\n      userId: user.id,\n      type: 'PURCHASE',\n      amount: investmentAmount,\n      description: `Mining unit purchase - ${thsAmount} TH/s`,\n      status: 'COMPLETED',\n    });\n\n    // Note: User active status is now computed dynamically for binary tree display\n\n    // Process referral commissions and binary points\n    const sponsorInfo = await getSponsorInfo(user.id);\n    if (sponsorInfo) {\n      try {\n        // Process direct referral bonus (10% to sponsor's wallet)\n        const bonusAmount = await processDirectReferralBonus(sponsorInfo.id, investmentAmount, user.id);\n        console.log(`Direct referral bonus of $${bonusAmount} added to sponsor ${sponsorInfo.id}`);\n\n        // Add binary points to active upliners ($100 = 1 point)\n        await addBinaryPoints(user.id, investmentAmount);\n        console.log(`Binary points added for investment of $${investmentAmount}`);\n      } catch (commissionError) {\n        console.error('Error processing commissions:', commissionError);\n        // Don't fail the purchase if commission processing fails\n      }\n    }\n\n    // Log the purchase\n    await systemLogDb.create({\n      action: 'MINING_UNIT_PURCHASED',\n      userId: user.id,\n      details: {\n        miningUnitId: miningUnit.id,\n        thsAmount,\n        investmentAmount,\n        dailyROI,\n        sponsorId: sponsorInfo?.id,\n      },\n      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',\n      userAgent: request.headers.get('user-agent') || 'unknown',\n    });\n\n    return NextResponse.json({\n      success: true,\n      message: 'Mining unit purchased successfully',\n      data: miningUnit,\n    });\n\n  } catch (error: any) {\n    console.error('Mining unit purchase error:', error);\n    \n    return NextResponse.json(\n      { success: false, error: 'Failed to purchase mining unit' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAE1D,IAAI,CAAC,iBAAiB,CAAC,MAAM;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,MAAM,wHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC,KAAK,EAAE;QAEjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAE3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA+B,GACxD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAE1D,IAAI,CAAC,iBAAiB,CAAC,MAAM;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG;QAExC,aAAa;QACb,IAAI,CAAC,aAAa,CAAC,oBAAoB,aAAa,KAAK,oBAAoB,GAAG;YAC9E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA2C,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,kDAAkD;QAClD,MAAM,cAAc,WAAW,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,uBAAuB;QAChF,IAAI,mBAAmB,aAAa;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,CAAC,4BAA4B,EAAE,aAAa;YAAC,GACtE;gBAAE,QAAQ;YAAI;QAElB;QAEA,qCAAqC;QACrC,MAAM,WAAW,WAAW,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,gBAAgB;QACtE,MAAM,iBAAiB,YAAY;QAEnC,+CAA+C;QAC/C,IAAI,KAAK,GAAG,CAAC,mBAAmB,kBAAkB,iBAAiB,MAAM;YACvE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA8C,GACvE;gBAAE,QAAQ;YAAI;QAElB;QAEA,2CAA2C;QAC3C,MAAM,EAAE,mBAAmB,EAAE,GAAG;QAChC,MAAM,aAAa,MAAM,oBAAoB;QAE7C,uCAAuC;QACvC,MAAM,gBAAgB,MAAM,wHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,KAAK,EAAE;QAE/D,IAAI,cAAc,gBAAgB,GAAG,kBAAkB;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO,CAAC,kCAAkC,EAAE,cAAc,gBAAgB,CAAC,OAAO,CAAC,GAAG,aAAa,EAAE,iBAAiB,OAAO,CAAC,IAAI;YACpI,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,WAAW;QAEjB,qBAAqB;QACrB,MAAM,aAAa,MAAM,wHAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAC3C,QAAQ,KAAK,EAAE;YACf;YACA;YACA;QACF;QAEA,oCAAoC;QACpC,MAAM,wHAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE;YAC3C,kBAAkB,cAAc,gBAAgB,GAAG;QACrD;QAEA,8BAA8B;QAC9B,MAAM,wHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;YACzB,QAAQ,KAAK,EAAE;YACf,MAAM;YACN,QAAQ;YACR,aAAa,CAAC,uBAAuB,EAAE,UAAU,KAAK,CAAC;YACvD,QAAQ;QACV;QAEA,+EAA+E;QAE/E,iDAAiD;QACjD,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;QAChD,IAAI,aAAa;YACf,IAAI;gBACF,0DAA0D;gBAC1D,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,6BAA0B,AAAD,EAAE,YAAY,EAAE,EAAE,kBAAkB,KAAK,EAAE;gBAC9F,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,kBAAkB,EAAE,YAAY,EAAE,EAAE;gBAEzF,wDAAwD;gBACxD,MAAM,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE,EAAE;gBAC/B,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,kBAAkB;YAC1E,EAAE,OAAO,iBAAiB;gBACxB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,yDAAyD;YAC3D;QACF;QAEA,mBAAmB;QACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,QAAQ,KAAK,EAAE;YACf,SAAS;gBACP,cAAc,WAAW,EAAE;gBAC3B;gBACA;gBACA;gBACA,WAAW,aAAa;YAC1B;YACA,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;YACrD,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAClD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAE7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAiC,GAC1D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}