const CHUNK_PUBLIC_PATH = "server/app/api/admin/users/[userId]/details/route.js";
const runtime = require("../../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_bd540014._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__a47c1ca1._.js");
runtime.loadChunk("server/chunks/node_modules_5284e5f6._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/users/[userId]/details/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/users/[userId]/details/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/users/[userId]/details/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
