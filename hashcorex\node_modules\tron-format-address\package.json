{"name": "tron-format-address", "version": "0.1.12", "description": "Tron utility to convert address from hex to base58 and vice versa", "main": "build/lib/crypto.js", "types": "build/lib/crypto.d.ts", "scripts": {"test": "npx mocha -r ts-node/register 'test/**/*.ts'", "build": "npx tsc", "lint": "npx eslint 'lib/**/*.{ts,tsx}'", "lint:fix": "npx eslint 'lib/**/*.{ts,tsx}' --fix", "format": "npx prettier --write \"**/*.ts\""}, "repository": {"type": "git", "url": "git+ssh://**************/kushkamisha/tron-format-address.git"}, "keywords": ["tron", "address", "format", "hex", "base58", "toHex", "fromHex"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/kushkamisha/tron-format-address#readme", "devDependencies": {"@types/chai": "^4.2.15", "@types/mocha": "^10.0.1", "@types/node": "^20.2.5", "@typescript-eslint/eslint-plugin": "^4.15.0", "@typescript-eslint/parser": "^4.15.0", "chai": "^4.3.0", "eslint": "^7.19.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.2.1", "mocha": "^10.2.0", "prettier": "^3.0.3", "ts-node": "^10.9.1", "typescript": "^5.0.3"}, "bugs": {"url": "https://github.com/kushkamisha/tron-format-address/issues"}}